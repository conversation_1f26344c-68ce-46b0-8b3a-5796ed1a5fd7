import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import type { 
  Audit,
  AuditDetailResponse,
  CreateAuditForm,
  AuditQueryParams,
  AuditProgress,
  AuditStatistics
} from '@/types/audit'
import { auditApi } from '@/api/audit'
import { message } from 'ant-design-vue'
import { websocketManager } from '@/utils/websocket'

export const useAuditStore = defineStore('audit', () => {
  const audits = ref<Audit[]>([])
  const currentAudit = ref<AuditDetailResponse | null>(null)
  const auditProgress = reactive<Record<number, AuditProgress>>({})
  const statistics = ref<AuditStatistics | null>(null)
  const loading = ref(false)
  const total = ref(0)

  // 获取审计列表
  const fetchAudits = async (params?: AuditQueryParams) => {
    loading.value = true
    try {
      const response = await auditApi.getAudits(params)
      audits.value = response.data
      total.value = response.data.length
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取审计列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取审计详情
  const fetchAudit = async (id: number) => {
    loading.value = true
    try {
      const response = await auditApi.getAudit(id)
      currentAudit.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取审计详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建审计
  const createAudit = async (auditForm: CreateAuditForm) => {
    loading.value = true
    try {
      const response = await auditApi.createAudit(auditForm)
      audits.value.unshift(response.data)
      message.success('审计任务创建成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '创建审计任务失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取审计进度
  const fetchAuditProgress = async (id: number) => {
    try {
      const response = await auditApi.getAuditProgress(id)
      auditProgress[id] = response.data
      return response
    } catch (error: any) {
      console.error('获取审计进度失败:', error)
      throw error
    }
  }

  // 取消审计
  const cancelAudit = async (id: number, reason?: string) => {
    loading.value = true
    try {
      const response = await auditApi.cancelAudit(id, { reason })
      
      // 更新本地状态
      const audit = audits.value.find(a => a.id === id)
      if (audit) {
        audit.status = 'failed'
      }
      
      if (currentAudit.value?.id === id) {
        currentAudit.value.status = 'failed'
      }
      
      message.success('审计任务已取消')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '取消审计任务失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重新运行审计
  const rerunAudit = async (id: number) => {
    loading.value = true
    try {
      const response = await auditApi.rerunAudit(id)
      
      // 更新本地状态
      const index = audits.value.findIndex(a => a.id === id)
      if (index !== -1) {
        audits.value[index] = response.data
      }
      
      if (currentAudit.value?.id === id) {
        Object.assign(currentAudit.value, response.data)
      }
      
      message.success('审计任务已重新启动')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '重新运行审计失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取审计统计
  const fetchStatistics = async () => {
    try {
      const response = await auditApi.getAuditStatistics()
      statistics.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取审计统计失败')
      throw error
    }
  }

  // 更新审计进度（通过WebSocket）
  const updateAuditProgress = (auditId: number, progress: AuditProgress) => {
    auditProgress[auditId] = progress
    
    // 更新审计列表中的状态
    const audit = audits.value.find(a => a.id === auditId)
    if (audit) {
      audit.status = progress.status
    }
    
    // 更新当前审计的状态
    if (currentAudit.value?.id === auditId) {
      currentAudit.value.status = progress.status
    }
  }

  // 初始化WebSocket监听
  const initWebSocketListeners = () => {
    // 监听审计进度更新
    websocketManager.onAuditProgress((data) => {
      updateAuditProgress(data.audit_id, data.progress)
    })

    // 监听审计状态更新
    websocketManager.onAuditStatus((data) => {
      const audit = audits.value.find(a => a.id === data.audit_id)
      if (audit) {
        audit.status = data.status
      }
      
      if (currentAudit.value?.id === data.audit_id) {
        currentAudit.value.status = data.status
      }
    })

    // 监听审计日志
    websocketManager.onAuditLog((data) => {
      if (currentAudit.value?.id === data.audit_id) {
        currentAudit.value.logs.push(data.log)
      }
    })
  }

  // 清理进度数据
  const clearProgress = (auditId: number) => {
    delete auditProgress[auditId]
  }

  return {
    audits,
    currentAudit,
    auditProgress,
    statistics,
    loading,
    total,
    fetchAudits,
    fetchAudit,
    createAudit,
    fetchAuditProgress,
    cancelAudit,
    rerunAudit,
    fetchStatistics,
    updateAuditProgress,
    initWebSocketListeners,
    clearProgress
  }
})
