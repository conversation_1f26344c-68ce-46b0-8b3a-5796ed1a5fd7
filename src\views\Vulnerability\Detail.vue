<template>
  <div class="vulnerability-detail">
    <a-page-header
      :title="`漏洞 #${vulnerability?.id}`"
      :sub-title="vulnerability?.description"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleRefresh" :loading="vulnerabilityStore.loading">
            <template #icon>
              <SyncOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
    </a-page-header>
    
    <a-row :gutter="[16, 16]">
      <!-- 漏洞基本信息 -->
      <a-col :span="24">
        <a-card title="漏洞信息" :loading="vulnerabilityStore.loading">
          <a-descriptions :column="3" v-if="vulnerability">
            <a-descriptions-item label="漏洞ID">#{{ vulnerability.id }}</a-descriptions-item>
            <a-descriptions-item label="文件路径">
              <a-typography-text 
                :copyable="{ text: vulnerability.file_path }"
                style="font-family: monospace; font-size: 12px;"
              >
                {{ vulnerability.file_path }}
              </a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="行号">
              {{ vulnerability.line_number || '未指定' }}
            </a-descriptions-item>
            
            <a-descriptions-item label="严重程度">
              <a-tag :color="getSeverityColor(vulnerability.severity)" size="large">
                {{ getSeverityText(vulnerability.severity) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="当前状态">
              <a-tag :color="getStatusColor(vulnerability.status)" size="large">
                {{ getStatusText(vulnerability.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="漏洞类型">
              <a-tag :color="getTypeColor(vulnerability.type)">
                {{ getTypeText(vulnerability.type) }}
              </a-tag>
            </a-descriptions-item>
            
            <a-descriptions-item label="关联审计">
              <a @click="$router.push(`/audit/detail/${vulnerability.audit_id}`)">
                审计 #{{ vulnerability.audit_id }}
              </a>
            </a-descriptions-item>
            <a-descriptions-item label="提交ID" v-if="vulnerability.commit_id">
              <a-typography-text 
                :copyable="{ text: vulnerability.commit_id }"
                style="font-family: monospace; font-size: 12px;"
              >
                {{ vulnerability.commit_id }}
              </a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(vulnerability.created_at) }}
            </a-descriptions-item>
            
            <a-descriptions-item label="更新时间">
              {{ formatDate(vulnerability.updated_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="状态原因" v-if="vulnerability.reason" :span="2">
              {{ vulnerability.reason }}
            </a-descriptions-item>
            
            <a-descriptions-item label="详细描述" :span="3">
              <div class="description-content">
                {{ vulnerability.description }}
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
      
      <!-- 状态更新 -->
      <a-col :span="24">
        <a-card title="状态管理">
          <a-form
            :model="statusForm"
            :rules="statusRules"
            @finish="handleStatusUpdate"
            layout="vertical"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="更新状态" name="status">
                  <a-select 
                    v-model:value="statusForm.status" 
                    size="large"
                    placeholder="选择新状态"
                  >
                    <a-select-option value="confirmed">
                      <a-tag color="red">已确认</a-tag>
                      <span style="margin-left: 8px;">确认此漏洞存在安全风险</span>
                    </a-select-option>
                    <a-select-option value="unexploitable">
                      <a-tag color="green">不可利用</a-tag>
                      <span style="margin-left: 8px;">漏洞存在但无法被利用</span>
                    </a-select-option>
                    <a-select-option value="manual_review">
                      <a-tag color="orange">待审核</a-tag>
                      <span style="margin-left: 8px;">需要人工进一步审核</span>
                    </a-select-option>
                    <a-select-option value="unknown">
                      <a-tag color="default">未知</a-tag>
                      <span style="margin-left: 8px;">状态未确定</span>
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="更新原因" name="reason">
                  <a-textarea
                    v-model:value="statusForm.reason"
                    placeholder="请说明状态更新的原因（可选）"
                    :rows="3"
                    size="large"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label=" ">
                  <a-button 
                    type="primary" 
                    html-type="submit" 
                    size="large"
                    :loading="updateLoading"
                    block
                  >
                    更新状态
                  </a-button>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 代码片段（如果有） -->
      <a-col :span="24" v-if="codeSnippet">
        <a-card title="代码片段">
          <pre class="code-snippet">{{ codeSnippet }}</pre>
        </a-card>
      </a-col>
      
      <!-- 修复建议（如果有） -->
      <a-col :span="24" v-if="fixSuggestion">
        <a-card title="修复建议">
          <div class="fix-suggestion">
            {{ fixSuggestion }}
          </div>
        </a-card>
      </a-col>
      
      <!-- 相关链接（如果有） -->
      <a-col :span="24" v-if="references && references.length > 0">
        <a-card title="相关链接">
          <a-list
            :data-source="references"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a :href="item" target="_blank" rel="noopener noreferrer">
                  {{ item }}
                </a>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
      
      <!-- 操作历史 -->
      <a-col :span="24">
        <a-card title="操作历史">
          <a-timeline>
            <a-timeline-item color="blue">
              <div class="timeline-item">
                <div class="timeline-time">{{ formatDate(vulnerability?.created_at) }}</div>
                <div class="timeline-content">漏洞被发现并创建</div>
              </div>
            </a-timeline-item>
            <a-timeline-item 
              v-if="vulnerability?.updated_at !== vulnerability?.created_at"
              color="green"
            >
              <div class="timeline-item">
                <div class="timeline-time">{{ formatDate(vulnerability?.updated_at) }}</div>
                <div class="timeline-content">
                  状态更新为 
                  <a-tag :color="getStatusColor(vulnerability?.status)" size="small">
                    {{ getStatusText(vulnerability?.status) }}
                  </a-tag>
                  <div v-if="vulnerability?.reason" class="timeline-reason">
                    原因：{{ vulnerability.reason }}
                  </div>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import { formatDate } from '@/utils/date'
import { SyncOutlined } from '@ant-design/icons-vue'
import type { 
  VulnerabilitySeverity, 
  VulnerabilityStatus,
  VulnerabilityType,
  VulnerabilityUpdateRequest
} from '@/types/vulnerability'

const route = useRoute()
const router = useRouter()
const vulnerabilityStore = useVulnerabilityStore()

const updateLoading = ref(false)

const vulnerability = computed(() => vulnerabilityStore.currentVulnerability)

const statusForm = reactive<VulnerabilityUpdateRequest>({
  status: undefined,
  reason: ''
})

const statusRules = {
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 模拟扩展数据（实际应该从API获取）
const codeSnippet = ref('')
const fixSuggestion = ref('')
const references = ref<string[]>([])

const getSeverityColor = (severity?: VulnerabilitySeverity) => {
  if (!severity) return 'default'
  const colorMap: Record<VulnerabilitySeverity, string> = {
    critical: 'red',
    high: 'orange',
    medium: 'gold',
    low: 'green'
  }
  return colorMap[severity] || 'default'
}

const getSeverityText = (severity?: VulnerabilitySeverity) => {
  if (!severity) return ''
  const textMap: Record<VulnerabilitySeverity, string> = {
    critical: '严重',
    high: '高危',
    medium: '中危',
    low: '低危'
  }
  return textMap[severity] || severity
}

const getStatusColor = (status?: VulnerabilityStatus) => {
  if (!status) return 'default'
  const colorMap: Record<VulnerabilityStatus, string> = {
    confirmed: 'red',
    unexploitable: 'green',
    manual_review: 'orange',
    unknown: 'default'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status?: VulnerabilityStatus) => {
  if (!status) return ''
  const textMap: Record<VulnerabilityStatus, string> = {
    confirmed: '已确认',
    unexploitable: '不可利用',
    manual_review: '待审核',
    unknown: '未知'
  }
  return textMap[status] || status
}

const getTypeColor = (type?: VulnerabilityType) => {
  if (!type) return 'default'
  const colorMap: Record<VulnerabilityType, string> = {
    sensitive_info: 'purple',
    file_audit: 'blue',
    entry_point: 'cyan',
    sensitive_func: 'magenta',
    new: 'green',
    fixed: 'lime',
    partial_fixed: 'orange'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type?: VulnerabilityType) => {
  if (!type) return ''
  const textMap: Record<VulnerabilityType, string> = {
    sensitive_info: '敏感信息',
    file_audit: '文件审计',
    entry_point: '入口点',
    sensitive_func: '敏感函数',
    new: '新增',
    fixed: '已修复',
    partial_fixed: '部分修复'
  }
  return textMap[type] || type
}

const handleStatusUpdate = async () => {
  if (!vulnerability.value) return
  
  updateLoading.value = true
  try {
    await vulnerabilityStore.updateVulnerabilityStatus(
      vulnerability.value.id, 
      statusForm
    )
    
    // 重置表单
    statusForm.status = undefined
    statusForm.reason = ''
  } catch (error) {
    console.error('Update status error:', error)
  } finally {
    updateLoading.value = false
  }
}

const handleRefresh = async () => {
  const vulnerabilityId = Number(route.params.id)
  if (vulnerabilityId) {
    await vulnerabilityStore.fetchVulnerability(vulnerabilityId)
  }
}

// 模拟获取扩展数据
const fetchExtendedData = () => {
  // 这里应该调用API获取代码片段、修复建议等扩展信息
  // 目前使用模拟数据
  if (vulnerability.value?.type === 'sensitive_info') {
    codeSnippet.value = `// 示例代码片段
const password = "hardcoded_password";
const apiKey = "sk-1234567890abcdef";`
    
    fixSuggestion.value = '建议将敏感信息移至环境变量或配置文件中，避免硬编码在源代码中。'
    
    references.value = [
      'https://owasp.org/www-project-top-ten/2017/A3_2017-Sensitive_Data_Exposure',
      'https://cwe.mitre.org/data/definitions/798.html'
    ]
  }
}

onMounted(async () => {
  const vulnerabilityId = Number(route.params.id)
  if (vulnerabilityId) {
    await vulnerabilityStore.fetchVulnerability(vulnerabilityId)
    fetchExtendedData()
  }
})
</script>

<style scoped>
.vulnerability-detail {
  padding: 0;
}

.description-content {
  white-space: pre-wrap;
  line-height: 1.6;
}

.code-snippet {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
  overflow-x: auto;
}

.fix-suggestion {
  padding: 16px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  line-height: 1.6;
}

.timeline-item {
  padding: 4px 0;
}

.timeline-time {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.timeline-content {
  font-size: 14px;
  line-height: 1.4;
}

.timeline-reason {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-select-selection-item) {
  display: flex;
  align-items: center;
}
</style>
