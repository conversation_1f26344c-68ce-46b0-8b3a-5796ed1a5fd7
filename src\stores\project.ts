import { defineStore } from 'pinia'
import { ref } from 'vue'
import type {
  Project,
  CreateProjectForm,
  UpdateProjectForm,
  ProjectQueryParams,
  GitRepositoryInfo
} from '@/types/project'
import { projectApi } from '@/api/project'
import { message } from 'ant-design-vue'

export const useProjectStore = defineStore('project', () => {
  const projects = ref<Project[]>([])
  const currentProject = ref<Project | null>(null)
  const gitInfo = ref<GitRepositoryInfo | null>(null)
  const loading = ref(false)
  const total = ref(0)

  // 获取项目列表
  const fetchProjects = async (params?: ProjectQueryParams) => {
    loading.value = true
    try {
      const response = await projectApi.getProjects(params)
      projects.value = response.data
      // 注意：API返回的是数组，不是分页对象，所以total需要从数组长度获取
      total.value = response.data.length
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取项目列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取项目详情
  const fetchProject = async (id: number) => {
    loading.value = true
    try {
      const response = await projectApi.getProject(id)
      currentProject.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取项目详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建项目
  const createProject = async (projectForm: CreateProjectForm) => {
    loading.value = true
    try {
      const response = await projectApi.createProject(projectForm)
      projects.value.unshift(response.data)
      message.success('项目创建成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '项目创建失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新项目
  const updateProject = async (id: number, projectData: UpdateProjectForm) => {
    loading.value = true
    try {
      const response = await projectApi.updateProject(id, projectData)
      const index = projects.value.findIndex(p => p.id === id)
      if (index !== -1) {
        projects.value[index] = response.data
      }
      if (currentProject.value?.id === id) {
        currentProject.value = response.data
      }
      message.success('项目更新成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '项目更新失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除项目
  const deleteProject = async (id: number) => {
    loading.value = true
    try {
      await projectApi.deleteProject(id)
      projects.value = projects.value.filter(p => p.id !== id)
      if (currentProject.value?.id === id) {
        currentProject.value = null
      }
      message.success('项目删除成功')
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '项目删除失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取Git仓库信息
  const fetchGitInfo = async (id: number) => {
    loading.value = true
    try {
      const response = await projectApi.getGitInfo(id)
      gitInfo.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取Git信息失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 刷新Git哈希
  const refreshGitHash = async (id: number) => {
    loading.value = true
    try {
      const response = await projectApi.refreshGitHash(id)
      if (currentProject.value?.id === id) {
        currentProject.value = response.data
      }
      message.success('Git哈希刷新成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || 'Git哈希刷新失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    projects,
    currentProject,
    gitInfo,
    loading,
    total,
    fetchProjects,
    fetchProject,
    createProject,
    updateProject,
    deleteProject,
    fetchGitInfo,
    refreshGitHash
  }
})
