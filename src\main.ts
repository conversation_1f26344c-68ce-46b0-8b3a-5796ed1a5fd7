import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './assets/styles/main.css'

import App from './App.vue'
import router from './router'
import { connectWebSocket } from './utils/websocket'
import clickOutside from './directives/clickOutside'

// ECharts
import ECharts from 'vue-echarts'
import { use } from 'echarts/core'
import {
  CanvasRenderer
} from 'echarts/renderers'
import {
  <PERSON><PERSON><PERSON>,
  LineChart,
  PieChart
} from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
} from 'echarts/components'

use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
])

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd)
app.component('v-chart', ECharts)

// 注册自定义指令
app.directive('click-outside', clickOutside)

app.mount('#app')

// 在用户登录后初始化WebSocket连接
// 这里可以在路由守卫中或用户store中调用
// connectWebSocket().catch(console.error)
