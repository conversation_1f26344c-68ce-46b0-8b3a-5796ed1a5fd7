<template>
  <div class="project-detail">
    <a-page-header
      :title="project?.name"
      :sub-title="project?.description"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleRefreshHash" :loading="refreshLoading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新Hash
          </a-button>
          <a-button type="primary" @click="handleEdit">
            <template #icon>
              <EditOutlined />
            </template>
            编辑项目
          </a-button>
          <a-popconfirm
            title="确定要删除这个项目吗？"
            @confirm="handleDeleteProject"
            ok-text="确定"
            cancel-text="取消"
          >
            <a-button danger>
              <template #icon>
                <DeleteOutlined />
              </template>
              删除项目
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </a-page-header>
    
    <a-row :gutter="[16, 16]">
      <!-- 项目基本信息 -->
      <a-col :span="24">
        <a-card title="项目信息" :loading="projectStore.loading">
          <a-descriptions :column="2" v-if="project">
            <a-descriptions-item label="项目名称">{{ project.name }}</a-descriptions-item>
            <a-descriptions-item label="项目类型">
              <a-tag :color="getTypeColor(project.type)">
                {{ getTypeText(project.type) }}
              </a-tag>
            </a-descriptions-item>
            
            <template v-if="project.type === 'git'">
              <a-descriptions-item label="Git仓库">
                <a :href="project.git_url" target="_blank">{{ project.git_url }}</a>
              </a-descriptions-item>
              <a-descriptions-item label="Git分支">
                <a-tag color="blue">{{ project.git_branch || 'main' }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="当前哈希">
                <a-typography-text 
                  v-if="project.current_hash" 
                  :copyable="{ text: project.current_hash }"
                  style="font-family: monospace;"
                >
                  {{ project.current_hash }}
                </a-typography-text>
                <span v-else class="text-gray">未获取</span>
              </a-descriptions-item>
            </template>
            
            <template v-if="project.type === 'docker'">
              <a-descriptions-item label="Docker镜像">{{ project.docker_image }}</a-descriptions-item>
            </template>
            
            <a-descriptions-item label="源码路径">
              {{ project.source_path || '根目录' }}
            </a-descriptions-item>
            <a-descriptions-item label="检查间隔">
              {{ project.check_interval }} 分钟
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(project.created_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ formatDate(project.updated_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="项目描述" :span="2">
              {{ project.description || '无描述' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
      
      <!-- Git仓库信息 -->
      <a-col :span="24" v-if="project?.type === 'git'">
        <a-card 
          title="Git仓库信息" 
          :loading="gitInfoLoading"
          :extra="gitInfoExtra"
        >
          <a-descriptions :column="2" v-if="gitInfo">
            <a-descriptions-item label="仓库URL">
              <a :href="gitInfo.url" target="_blank">{{ gitInfo.url }}</a>
            </a-descriptions-item>
            <a-descriptions-item label="分支">
              <a-tag color="blue">{{ gitInfo.branch }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="当前哈希">
              <a-typography-text 
                v-if="gitInfo.current_hash" 
                :copyable="{ text: gitInfo.current_hash }"
                style="font-family: monospace;"
              >
                {{ gitInfo.current_hash }}
              </a-typography-text>
            </a-descriptions-item>
            <a-descriptions-item label="可访问性">
              <a-tag :color="gitInfo.is_accessible ? 'green' : 'red'">
                {{ gitInfo.is_accessible ? '可访问' : '不可访问' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="最后提交信息" :span="2" v-if="gitInfo.last_commit_message">
              {{ gitInfo.last_commit_message }}
            </a-descriptions-item>
            <a-descriptions-item label="提交作者" v-if="gitInfo.last_commit_author">
              {{ gitInfo.last_commit_author }}
            </a-descriptions-item>
            <a-descriptions-item label="提交时间" v-if="gitInfo.last_commit_date">
              {{ formatDate(gitInfo.last_commit_date) }}
            </a-descriptions-item>
          </a-descriptions>
          <a-empty v-else description="暂无Git仓库信息" />
        </a-card>
      </a-col>
      
      <!-- 扩展数据 -->
      <a-col :span="24" v-if="project?.extra_data && Object.keys(project.extra_data).length > 0">
        <a-card title="扩展数据">
          <pre class="extra-data">{{ JSON.stringify(project.extra_data, null, 2) }}</pre>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 编辑项目模态框 -->
    <a-modal
      v-model:open="showEditModal"
      title="编辑项目"
      @ok="handleUpdateProject"
      @cancel="handleCancelEdit"
      :confirm-loading="projectStore.loading"
      width="600px"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        layout="vertical"
      >
        <a-form-item label="项目名称" name="name">
          <a-input v-model:value="editForm.name" placeholder="请输入项目名称" />
        </a-form-item>
        
        <a-form-item label="项目描述" name="description">
          <a-textarea
            v-model:value="editForm.description"
            placeholder="请输入项目描述"
            :rows="3"
          />
        </a-form-item>
        
        <template v-if="project?.type === 'git'">
          <a-form-item label="Git仓库URL" name="git_url">
            <a-input v-model:value="editForm.git_url" placeholder="请输入Git仓库URL" />
          </a-form-item>
          
          <a-form-item label="Git分支" name="git_branch">
            <a-input v-model:value="editForm.git_branch" placeholder="请输入Git分支" />
          </a-form-item>
        </template>
        
        <template v-if="project?.type === 'docker'">
          <a-form-item label="Docker镜像" name="docker_image">
            <a-input v-model:value="editForm.docker_image" placeholder="请输入Docker镜像" />
          </a-form-item>
        </template>
        
        <a-form-item label="源码路径" name="source_path">
          <a-input v-model:value="editForm.source_path" placeholder="请输入源码路径" />
        </a-form-item>
        
        <a-form-item label="检查间隔（分钟）" name="check_interval">
          <a-input-number 
            v-model:value="editForm.check_interval" 
            :min="1" 
            :max="10080"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { formatDate } from '@/utils/date'
import { 
  ReloadOutlined, 
  EditOutlined, 
  DeleteOutlined,
  SyncOutlined
} from '@ant-design/icons-vue'
import type { Project, UpdateProjectForm, GitRepositoryInfo } from '@/types/project'
import type { FormInstance } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const projectStore = useProjectStore()

const project = computed(() => projectStore.currentProject)
const gitInfo = computed(() => projectStore.gitInfo)

const showEditModal = ref(false)
const refreshLoading = ref(false)
const gitInfoLoading = ref(false)
const editFormRef = ref<FormInstance>()

const editForm = reactive<UpdateProjectForm>({
  name: '',
  description: '',
  git_url: '',
  git_branch: '',
  docker_image: '',
  source_path: '',
  check_interval: 1440
})

const editRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 1, max: 255, message: '项目名称长度为1-255个字符', trigger: 'blur' }
  ],
  git_url: [
    { max: 512, message: 'Git URL长度不能超过512个字符', trigger: 'blur' }
  ],
  check_interval: [
    { required: true, message: '请输入检查间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 10080, message: '检查间隔范围为1-10080分钟', trigger: 'blur' }
  ]
}

const gitInfoExtra = computed(() => {
  if (project.value?.type !== 'git') return null
  
  return h('a-button', {
    size: 'small',
    loading: gitInfoLoading.value,
    onClick: handleFetchGitInfo
  }, {
    icon: () => h(SyncOutlined),
    default: () => '刷新Git信息'
  })
})

const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    git: 'blue',
    docker: 'green'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    git: 'Git项目',
    docker: 'Docker项目'
  }
  return textMap[type] || type
}

const handleRefreshHash = async () => {
  if (!project.value) return
  
  refreshLoading.value = true
  try {
    await projectStore.refreshGitHash(project.value.id)
  } catch (error) {
    console.error('Refresh hash error:', error)
  } finally {
    refreshLoading.value = false
  }
}

const handleFetchGitInfo = async () => {
  if (!project.value) return
  
  gitInfoLoading.value = true
  try {
    await projectStore.fetchGitInfo(project.value.id)
  } catch (error) {
    console.error('Fetch git info error:', error)
  } finally {
    gitInfoLoading.value = false
  }
}

const handleDeleteProject = async () => {
  if (!project.value) return
  
  try {
    await projectStore.deleteProject(project.value.id)
    router.push('/project/list')
  } catch (error) {
    console.error('Delete project error:', error)
  }
}

const handleEdit = () => {
  if (!project.value) return
  
  Object.assign(editForm, {
    name: project.value.name,
    description: project.value.description,
    git_url: project.value.git_url,
    git_branch: project.value.git_branch,
    docker_image: project.value.docker_image,
    source_path: project.value.source_path,
    check_interval: project.value.check_interval
  })
  showEditModal.value = true
}

const handleUpdateProject = async () => {
  try {
    await editFormRef.value?.validate()
    if (project.value) {
      await projectStore.updateProject(project.value.id, editForm)
      showEditModal.value = false
    }
  } catch (error) {
    console.error('Update project error:', error)
  }
}

const handleCancelEdit = () => {
  showEditModal.value = false
  editFormRef.value?.resetFields()
}

onMounted(async () => {
  const projectId = Number(route.params.id)
  if (projectId) {
    await projectStore.fetchProject(projectId)
    
    // 如果是Git项目，自动获取Git信息
    if (project.value?.type === 'git') {
      handleFetchGitInfo()
    }
  }
})
</script>

<style scoped>
.project-detail {
  padding: 0;
}

.text-gray {
  color: #999;
}

.extra-data {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}
</style>
