// 基于API文档的漏洞类型定义
export type VulnerabilitySeverity = 'low' | 'medium' | 'high' | 'critical'
export type VulnerabilityStatus = 'confirmed' | 'unexploitable' | 'manual_review' | 'unknown'
export type VulnerabilityType = 'sensitive_info' | 'file_audit' | 'entry_point' | 'sensitive_func' | 'new' | 'fixed' | 'partial_fixed'

export interface Vulnerability {
  id: number
  file_path: string
  line_number?: number
  type: VulnerabilityType
  description: string
  severity: VulnerabilitySeverity
  audit_id: number
  status: VulnerabilityStatus
  reason?: string
  commit_id?: string
  created_at: string
  updated_at: string
  // 关联数据
  audit_name?: string
  project_name?: string
  project_id?: number
}

export interface VulnerabilityCreate {
  file_path: string
  line_number?: number
  type: VulnerabilityType
  description: string
  severity?: VulnerabilitySeverity
  audit_id: number
  commit_id?: string
}

export interface VulnerabilityUpdate {
  status?: VulnerabilityStatus
  reason?: string
  severity?: VulnerabilitySeverity
}

export interface VulnerabilityUpdateRequest {
  status?: VulnerabilityStatus
  reason?: string
  severity?: VulnerabilitySeverity
}

export interface VulnerabilityBulkUpdate {
  vulnerability_ids: number[]
  status?: VulnerabilityStatus
  reason?: string
  severity?: VulnerabilitySeverity
}

export interface VulnerabilityQueryParams {
  audit_id?: number
  project_id?: number
  severity?: VulnerabilitySeverity
  status?: VulnerabilityStatus
  type?: VulnerabilityType
  file_path?: string
  skip?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface VulnerabilityFilterParams {
  audit_ids?: number[]
  project_ids?: number[]
  severities?: VulnerabilitySeverity[]
  statuses?: VulnerabilityStatus[]
  types?: VulnerabilityType[]
  file_paths?: string[]
  date_from?: string
  date_to?: string
  skip?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface VulnerabilityStatsResponse {
  total: number
  by_severity: Record<VulnerabilitySeverity, number>
  by_status: Record<VulnerabilityStatus, number>
  by_type: Record<VulnerabilityType, number>
  recent_changes: {
    last_24h: number
    last_7d: number
    last_30d: number
  }
}

export interface VulnerabilityTrendData {
  date: string
  count: number
  severity_breakdown: Record<VulnerabilitySeverity, number>
}

// 表格展开行数据
export interface VulnerabilityExpandedData {
  code_snippet?: string
  fix_suggestion?: string
  references?: string[]
  cve_id?: string
  cvss_score?: number
}
