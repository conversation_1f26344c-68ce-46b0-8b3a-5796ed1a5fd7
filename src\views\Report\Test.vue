<template>
  <div class="report-test">
    <a-card title="报告功能测试">
      <a-space direction="vertical" size="large" style="width: 100%">
        <!-- Markdown渲染测试 -->
        <a-card size="small" title="Markdown渲染测试">
          <a-button @click="testMarkdown">测试Markdown渲染</a-button>
          <div v-if="markdownContent" class="markdown-preview">
            <h4>原始Markdown:</h4>
            <pre>{{ sampleMarkdown }}</pre>
            <h4>渲染结果:</h4>
            <div class="markdown-content" v-html="markdownContent"></div>
          </div>
        </a-card>
        
        <!-- 文件下载测试 -->
        <a-card size="small" title="文件下载测试">
          <a-space>
            <a-button @click="testDownloadText">下载文本文件</a-button>
            <a-button @click="testDownloadJSON">下载JSON文件</a-button>
            <a-button @click="testDownloadMarkdown">下载Markdown文件</a-button>
          </a-space>
        </a-card>
        
        <!-- 报告模板预览 -->
        <a-card size="small" title="报告模板预览">
          <a-button @click="generateSampleReport">生成示例报告</a-button>
          <div v-if="sampleReport" class="sample-report">
            <div class="markdown-content" v-html="sampleReport"></div>
          </div>
        </a-card>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { marked } from 'marked'
import { downloadTextAsFile } from '@/utils/common'

const markdownContent = ref('')
const sampleReport = ref('')

const sampleMarkdown = `# 安全审计报告

## 执行摘要

本次安全审计发现了 **5个** 安全漏洞，其中包括：
- 1个严重漏洞
- 2个高危漏洞  
- 2个中危漏洞

## 漏洞详情

### 1. SQL注入漏洞 (严重)

**文件**: \`/src/controllers/user.js\`  
**行号**: 45  
**描述**: 用户输入未经过滤直接拼接到SQL查询中

\`\`\`javascript
const query = "SELECT * FROM users WHERE id = " + userId;
\`\`\`

**修复建议**: 使用参数化查询或ORM框架

### 2. XSS漏洞 (高危)

**文件**: \`/src/views/profile.html\`  
**行号**: 23  
**描述**: 用户输入未经转义直接输出到页面

## 修复建议

1. 对所有用户输入进行验证和过滤
2. 使用参数化查询防止SQL注入
3. 对输出内容进行HTML转义
4. 实施内容安全策略(CSP)

## 统计信息

| 严重程度 | 数量 |
|---------|------|
| 严重    | 1    |
| 高危    | 2    |
| 中危    | 2    |
| 低危    | 0    |

---
*报告生成时间: 2024-01-15 10:30:00*`

const testMarkdown = () => {
  try {
    markdownContent.value = marked(sampleMarkdown)
  } catch (error) {
    console.error('Markdown render error:', error)
  }
}

const testDownloadText = () => {
  const content = '这是一个测试文本文件的内容。\n包含多行文本。'
  downloadTextAsFile(content, 'test.txt', 'text/plain')
}

const testDownloadJSON = () => {
  const data = {
    report_id: 1,
    audit_id: 123,
    vulnerabilities: [
      { id: 1, severity: 'high', description: 'SQL注入漏洞' },
      { id: 2, severity: 'medium', description: 'XSS漏洞' }
    ],
    generated_at: new Date().toISOString()
  }
  downloadTextAsFile(JSON.stringify(data, null, 2), 'report.json', 'application/json')
}

const testDownloadMarkdown = () => {
  downloadTextAsFile(sampleMarkdown, 'sample-report.md', 'text/markdown')
}

const generateSampleReport = () => {
  const reportTemplate = `# VulnAuditBox 安全审计报告

**项目名称**: 示例Web应用  
**审计时间**: ${new Date().toLocaleString()}  
**审计类型**: 初始审计

---

## 📊 审计概览

本次安全审计共扫描了 **1,234** 行代码，发现了 **8个** 潜在安全问题。

### 漏洞分布

| 严重程度 | 数量 | 百分比 |
|---------|------|--------|
| 🔴 严重  | 1    | 12.5%  |
| 🟠 高危  | 3    | 37.5%  |
| 🟡 中危  | 3    | 37.5%  |
| 🟢 低危  | 1    | 12.5%  |

---

## 🔍 主要发现

### 1. SQL注入漏洞 (严重)

**位置**: \`src/api/users.js:42\`  
**风险等级**: 🔴 严重  
**CVSS评分**: 9.8

**问题描述**:  
在用户查询接口中发现SQL注入漏洞，攻击者可以通过构造恶意输入获取敏感数据。

**代码片段**:
\`\`\`javascript
// 存在问题的代码
const query = \`SELECT * FROM users WHERE id = \${userId}\`;
db.query(query);
\`\`\`

**修复建议**:
\`\`\`javascript
// 修复后的代码
const query = 'SELECT * FROM users WHERE id = ?';
db.query(query, [userId]);
\`\`\`

### 2. 跨站脚本攻击 (XSS) (高危)

**位置**: \`src/components/UserProfile.vue:156\`  
**风险等级**: 🟠 高危  
**CVSS评分**: 7.4

**问题描述**:  
用户输入未经过滤直接渲染到页面，可能导致XSS攻击。

---

## 🛠️ 修复优先级

1. **立即修复** (严重): SQL注入漏洞
2. **本周内修复** (高危): XSS漏洞、文件上传漏洞、权限绕过
3. **本月内修复** (中危): 信息泄露、弱密码策略、会话管理
4. **计划修复** (低危): 版本信息泄露

---

## 📈 安全建议

### 开发阶段
- 实施安全编码规范
- 进行代码审查
- 使用静态代码分析工具

### 部署阶段  
- 配置Web应用防火墙(WAF)
- 启用HTTPS加密
- 实施内容安全策略(CSP)

### 运维阶段
- 定期安全扫描
- 监控异常访问
- 及时更新依赖库

---

## 📋 详细漏洞清单

| ID | 文件路径 | 行号 | 类型 | 严重程度 | 状态 |
|----|----------|------|------|----------|------|
| 1  | src/api/users.js | 42 | SQL注入 | 严重 | 待修复 |
| 2  | src/components/UserProfile.vue | 156 | XSS | 高危 | 待修复 |
| 3  | src/upload/handler.js | 23 | 文件上传 | 高危 | 待修复 |
| 4  | src/auth/middleware.js | 78 | 权限绕过 | 高危 | 待修复 |
| 5  | src/config/database.js | 12 | 信息泄露 | 中危 | 待修复 |
| 6  | src/auth/password.js | 34 | 弱密码 | 中危 | 待修复 |
| 7  | src/session/manager.js | 67 | 会话管理 | 中危 | 待修复 |
| 8  | src/utils/version.js | 5 | 信息泄露 | 低危 | 待修复 |

---

## 📞 联系信息

如有任何疑问，请联系安全团队：
- 邮箱: <EMAIL>
- 电话: +86-400-123-4567

---

*本报告由 VulnAuditBox 自动生成 | 生成时间: ${new Date().toLocaleString()}*`

  try {
    sampleReport.value = marked(reportTemplate)
  } catch (error) {
    console.error('Sample report render error:', error)
  }
}
</script>

<style scoped>
.report-test {
  max-width: 1000px;
  margin: 0 auto;
}

.markdown-preview {
  margin-top: 16px;
}

.markdown-preview h4 {
  margin: 16px 0 8px 0;
  color: #262626;
}

.markdown-preview pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.sample-report {
  margin-top: 16px;
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3) {
  margin-top: 24px;
  margin-bottom: 16px;
  color: #262626;
}

.markdown-content :deep(h1) {
  font-size: 28px;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.markdown-content :deep(h2) {
  font-size: 22px;
  border-bottom: 1px solid #d9d9d9;
  padding-bottom: 4px;
}

.markdown-content :deep(h3) {
  font-size: 18px;
}

.markdown-content :deep(p) {
  margin-bottom: 16px;
}

.markdown-content :deep(code) {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.markdown-content :deep(pre) {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
  border-left: 4px solid #1890ff;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
}

.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content :deep(th) {
  background: #fafafa;
  font-weight: 500;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #1890ff;
  padding-left: 16px;
  margin: 16px 0;
  color: #666;
  font-style: italic;
}

.markdown-content :deep(hr) {
  border: none;
  border-top: 1px solid #d9d9d9;
  margin: 24px 0;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  padding-left: 24px;
  margin-bottom: 16px;
}

.markdown-content :deep(li) {
  margin-bottom: 4px;
}
</style>
