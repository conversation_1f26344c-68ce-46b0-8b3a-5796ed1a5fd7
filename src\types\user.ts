// 基于API文档的用户类型定义
export interface User {
  id: number
  username: string
  email: string
  is_active: boolean
  created_at: string
  updated_at: string
  last_login_at?: string
}

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirm_password: string
}

export interface ChangePasswordForm {
  current_password: string
  new_password: string
  confirm_password: string
}

export interface UserUpdate {
  username?: string
  email?: string
  password?: string
  current_password?: string
}

// API响应类型
export interface Token {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

export interface UserRegisterResponse {
  user: User
  token: Token
}
