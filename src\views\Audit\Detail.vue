<template>
  <div class="audit-detail">
    <a-page-header
      :title="`审计任务 #${audit?.id}`"
      :sub-title="audit?.description"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-button 
            v-if="audit?.status === 'running'" 
            @click="handleCancel"
            danger
            :loading="cancelLoading"
          >
            <template #icon>
              <StopOutlined />
            </template>
            取消审计
          </a-button>
          <a-button 
            v-if="audit?.status === 'failed'" 
            @click="handleRerun"
            type="primary"
            :loading="rerunLoading"
          >
            <template #icon>
              <ReloadOutlined />
            </template>
            重新运行
          </a-button>
          <a-button @click="handleRefresh" :loading="auditStore.loading">
            <template #icon>
              <SyncOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
    </a-page-header>
    
    <a-row :gutter="[16, 16]">
      <!-- 审计基本信息 -->
      <a-col :span="24">
        <a-card title="审计信息" :loading="auditStore.loading">
          <a-descriptions :column="3" v-if="audit">
            <a-descriptions-item label="审计ID">#{{ audit.id }}</a-descriptions-item>
            <a-descriptions-item label="项目">
              <a @click="$router.push(`/project/detail/${audit.project_id}`)">
                {{ audit.project_name || `项目 #${audit.project_id}` }}
              </a>
            </a-descriptions-item>
            <a-descriptions-item label="审计类型">
              <a-tag :color="getTypeColor(audit.type)">
                {{ getTypeText(audit.type) }}
              </a-tag>
            </a-descriptions-item>
            
            <a-descriptions-item label="状态">
              <a-tag :color="getStatusColor(audit.status)">
                {{ getStatusText(audit.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="开始时间">
              {{ audit.start_time ? formatDate(audit.start_time) : '未开始' }}
            </a-descriptions-item>
            <a-descriptions-item label="结束时间">
              {{ audit.end_time ? formatDate(audit.end_time) : '未结束' }}
            </a-descriptions-item>
            
            <a-descriptions-item label="耗时">
              <span v-if="audit.duration">
                {{ formatDuration(audit.duration) }}
              </span>
              <span v-else-if="audit.status === 'running' && audit.start_time">
                {{ formatDuration(getRunningDuration(audit.start_time)) }}
              </span>
              <span v-else class="text-gray">-</span>
            </a-descriptions-item>
            <a-descriptions-item label="漏洞数量">
              {{ audit.vulnerability_count || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(audit.created_at) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
      
      <!-- 审计进度 -->
      <a-col :span="24" v-if="audit?.status === 'running' || progress">
        <a-card title="审计进度">
          <div v-if="progress">
            <a-progress 
              :percent="progress.progress" 
              :status="getProgressStatus(progress.status)"
              :stroke-color="getProgressColor(progress.progress)"
            />
            <div class="progress-info">
              <div v-if="progress.current_step" class="current-step">
                当前步骤: {{ progress.current_step }}
              </div>
              <div v-if="progress.estimated_time_remaining" class="estimated-time">
                预计剩余时间: {{ formatDuration(progress.estimated_time_remaining) }}
              </div>
              <div v-if="progress.message" class="progress-message">
                {{ progress.message }}
              </div>
            </div>
          </div>
          <a-empty v-else description="暂无进度信息" />
        </a-card>
      </a-col>
      
      <!-- 漏洞统计 -->
      <a-col :span="24" v-if="audit?.vulnerabilities_summary">
        <a-card title="漏洞统计">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :lg="4">
              <a-statistic
                title="总计"
                :value="audit.vulnerabilities_summary.total"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :lg="4">
              <a-statistic
                title="严重"
                :value="audit.vulnerabilities_summary.critical"
                :value-style="{ color: '#f5222d' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :lg="4">
              <a-statistic
                title="高危"
                :value="audit.vulnerabilities_summary.high"
                :value-style="{ color: '#fa541c' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :lg="4">
              <a-statistic
                title="中危"
                :value="audit.vulnerabilities_summary.medium"
                :value-style="{ color: '#faad14' }"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :lg="4">
              <a-statistic
                title="低危"
                :value="audit.vulnerabilities_summary.low"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
          </a-row>
        </a-card>
      </a-col>
      
      <!-- 审计日志 -->
      <a-col :span="24">
        <a-card title="审计日志">
          <div class="log-container" v-if="audit?.logs && audit.logs.length > 0">
            <div 
              v-for="log in audit.logs" 
              :key="log.id"
              class="log-item"
              :class="`log-${log.level}`"
            >
              <div class="log-header">
                <a-tag 
                  :color="getLogLevelColor(log.level)" 
                  size="small"
                >
                  {{ getLogLevelText(log.level) }}
                </a-tag>
                <span class="log-time">{{ formatDate(log.timestamp) }}</span>
              </div>
              <div class="log-message">{{ log.message }}</div>
            </div>
          </div>
          <a-empty v-else description="暂无日志信息" />
        </a-card>
      </a-col>
      
      <!-- 差异内容 -->
      <a-col :span="24" v-if="audit?.diff_content">
        <a-card title="代码差异">
          <pre class="diff-content">{{ audit.diff_content }}</pre>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuditStore } from '@/stores/audit'
import { formatDate } from '@/utils/date'
import { websocketManager, connectWebSocket } from '@/utils/websocket'
import { 
  StopOutlined, 
  ReloadOutlined, 
  SyncOutlined 
} from '@ant-design/icons-vue'
import type { AuditStatus, AuditType } from '@/types/audit'
import { Modal } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const auditStore = useAuditStore()

const cancelLoading = ref(false)
const rerunLoading = ref(false)
const progressTimer = ref<number>()

const audit = computed(() => auditStore.currentAudit)
const progress = computed(() => {
  if (!audit.value) return null
  return auditStore.auditProgress[audit.value.id]
})

const getTypeColor = (type: AuditType) => {
  const colorMap: Record<AuditType, string> = {
    initial: 'blue',
    update: 'green'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: AuditType) => {
  const textMap: Record<AuditType, string> = {
    initial: '初始审计',
    update: '更新审计'
  }
  return textMap[type] || type
}

const getStatusColor = (status: AuditStatus) => {
  const colorMap: Record<AuditStatus, string> = {
    pending: 'orange',
    running: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: AuditStatus) => {
  const textMap: Record<AuditStatus, string> = {
    pending: '待处理',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

const getProgressStatus = (status: AuditStatus) => {
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'exception'
  if (status === 'running') return 'active'
  return 'normal'
}

const getProgressColor = (percent: number) => {
  if (percent < 30) return '#f5222d'
  if (percent < 70) return '#faad14'
  return '#52c41a'
}

const getLogLevelColor = (level: string) => {
  const colorMap: Record<string, string> = {
    info: 'blue',
    warning: 'orange',
    error: 'red'
  }
  return colorMap[level] || 'default'
}

const getLogLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    info: '信息',
    warning: '警告',
    error: '错误'
  }
  return textMap[level] || level.toUpperCase()
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}时${minutes}分`
  }
}

const getRunningDuration = (startTime: string) => {
  const start = new Date(startTime).getTime()
  const now = Date.now()
  return Math.floor((now - start) / 1000)
}

const handleCancel = () => {
  Modal.confirm({
    title: '确认取消',
    content: '确定要取消这个审计任务吗？取消后无法恢复。',
    onOk: async () => {
      if (!audit.value) return
      
      cancelLoading.value = true
      try {
        await auditStore.cancelAudit(audit.value.id)
        await handleRefresh()
      } catch (error) {
        console.error('Cancel audit error:', error)
      } finally {
        cancelLoading.value = false
      }
    }
  })
}

const handleRerun = () => {
  Modal.confirm({
    title: '确认重新运行',
    content: '确定要重新运行这个审计任务吗？',
    onOk: async () => {
      if (!audit.value) return
      
      rerunLoading.value = true
      try {
        await auditStore.rerunAudit(audit.value.id)
        await handleRefresh()
      } catch (error) {
        console.error('Rerun audit error:', error)
      } finally {
        rerunLoading.value = false
      }
    }
  })
}

const handleRefresh = async () => {
  const auditId = Number(route.params.id)
  if (auditId) {
    await auditStore.fetchAudit(auditId)
    
    // 如果审计正在运行，获取进度信息
    if (audit.value?.status === 'running') {
      try {
        await auditStore.fetchAuditProgress(auditId)
      } catch (error) {
        console.error('Fetch progress error:', error)
      }
    }
  }
}

const startProgressPolling = () => {
  if (!audit.value || audit.value.status !== 'running') return
  
  progressTimer.value = window.setInterval(async () => {
    if (audit.value?.status === 'running') {
      try {
        await auditStore.fetchAuditProgress(audit.value.id)
      } catch (error) {
        console.error('Progress polling error:', error)
      }
    } else {
      stopProgressPolling()
    }
  }, 5000) // 每5秒轮询一次
}

const stopProgressPolling = () => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
    progressTimer.value = undefined
  }
}

onMounted(async () => {
  const auditId = Number(route.params.id)
  if (auditId) {
    await auditStore.fetchAudit(auditId)
    
    // 连接WebSocket
    try {
      await connectWebSocket()
      auditStore.initWebSocketListeners()
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      // 如果WebSocket连接失败，使用轮询
      startProgressPolling()
    }
    
    // 如果审计正在运行，获取进度信息
    if (audit.value?.status === 'running') {
      try {
        await auditStore.fetchAuditProgress(auditId)
      } catch (error) {
        console.error('Fetch progress error:', error)
      }
    }
  }
})

onUnmounted(() => {
  stopProgressPolling()
  
  // 清理进度数据
  if (audit.value) {
    auditStore.clearProgress(audit.value.id)
  }
})
</script>

<style scoped>
.audit-detail {
  padding: 0;
}

.text-gray {
  color: #999;
}

.progress-info {
  margin-top: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.current-step {
  font-weight: 500;
  margin-bottom: 4px;
}

.estimated-time,
.progress-message {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 3px solid #d9d9d9;
}

.log-item.log-info {
  background: #f6ffed;
  border-left-color: #52c41a;
}

.log-item.log-warning {
  background: #fffbe6;
  border-left-color: #faad14;
}

.log-item.log-error {
  background: #fff2f0;
  border-left-color: #f5222d;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.log-time {
  font-size: 12px;
  color: #666;
}

.log-message {
  font-size: 13px;
  line-height: 1.4;
}

.diff-content {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}
</style>
