{"name": "vulnauditbox-web", "private": true, "version": "1.0.0", "type": "module", "description": "VulnAuditBox - AI驱动的代码审计平台前端", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "ant-design-vue": "^4.1.0", "@ant-design/icons-vue": "^7.0.1", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "marked": "^11.1.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/node": "^20.10.5", "@types/lodash-es": "^4.17.12", "@types/js-cookie": "^3.0.6", "@vitejs/plugin-vue": "^4.5.2", "typescript": "^5.3.3", "vite": "^5.0.8", "vue-tsc": "^1.8.25", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "@vue/eslint-config-typescript": "^12.0.0"}, "engines": {"node": ">=18.0.0"}}