<template>
  <div class="webhook-list">
    <div class="webhook-header">
      <h2>Webhook管理</h2>
      <a-space>
        <a-button @click="handleRefresh" :loading="webhookStore.loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="$router.push('/webhook/create')">
          <template #icon>
            <PlusOutlined />
          </template>
          创建Webhook
        </a-button>
      </a-space>
    </div>
    
    <a-card>
      <div class="webhook-toolbar">
        <a-space>
          <a-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-space>
      </div>
      
      <a-table
        :columns="columns"
        :data-source="webhookStore.webhooks"
        :loading="webhookStore.loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :scroll="{ x: 1400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a @click="$router.push(`/webhook/detail/${record.id}`)">
              {{ record.name }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'url'">
            <a-tooltip :title="record.url">
              <span class="webhook-url">{{ getShortUrl(record.url) }}</span>
            </a-tooltip>
          </template>
          
          <template v-else-if="column.key === 'events'">
            <a-space wrap>
              <a-tag 
                v-for="event in record.events.slice(0, 3)" 
                :key="event"
                size="small"
                color="blue"
              >
                {{ event }}
              </a-tag>
              <a-tag v-if="record.events.length > 3" size="small" color="default">
                +{{ record.events.length - 3 }}
              </a-tag>
            </a-space>
          </template>
          
          <template v-else-if="column.key === 'is_active'">
            <a-switch
              :checked="record.is_active"
              @change="(checked) => handleToggleStatus(record.id, checked)"
              :loading="toggleLoading[record.id]"
            />
          </template>
          
          <template v-else-if="column.key === 'last_status'">
            <a-tag 
              v-if="record.last_status"
              :color="getStatusColor(record.last_status)"
              size="small"
            >
              {{ record.last_status }}
            </a-tag>
            <span v-else class="text-gray">-</span>
          </template>
          
          <template v-else-if="column.key === 'stats'">
            <div class="webhook-stats">
              <a-tooltip title="成功次数">
                <a-tag color="green" size="small">{{ record.success_count }}</a-tag>
              </a-tooltip>
              <a-tooltip title="失败次数">
                <a-tag color="red" size="small">{{ record.failure_count }}</a-tag>
              </a-tooltip>
            </div>
          </template>
          
          <template v-else-if="column.key === 'last_sent_at'">
            <span v-if="record.last_sent_at">
              {{ formatDate(record.last_sent_at) }}
            </span>
            <span v-else class="text-gray">从未发送</span>
          </template>
          
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a @click="$router.push(`/webhook/detail/${record.id}`)">查看</a>
              <a @click="handleTest(record.id)">测试</a>
              <a-popconfirm
                title="确定要删除这个Webhook吗？"
                @confirm="handleDelete(record.id)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a style="color: #ff4d4f">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useWebhookStore } from '@/stores/webhook'
import { formatDate } from '@/utils/date'
import { 
  ReloadOutlined, 
  PlusOutlined
} from '@ant-design/icons-vue'
import type { WebhookQueryParams } from '@/types/webhook'
import type { TableColumnsType } from 'ant-design-vue'

const router = useRouter()
const webhookStore = useWebhookStore()

const statusFilter = ref<boolean>()
const toggleLoading = ref<Record<number, boolean>>({})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns: TableColumnsType = [
  {
    title: 'Webhook名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    sorter: (a: any, b: any) => a.name.localeCompare(b.name)
  },
  {
    title: 'URL',
    dataIndex: 'url',
    key: 'url',
    width: 250,
    ellipsis: true
  },
  {
    title: '监听事件',
    dataIndex: 'events',
    key: 'events',
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'is_active',
    key: 'is_active',
    width: 80,
    filters: [
      { text: '启用', value: true },
      { text: '禁用', value: false }
    ],
    onFilter: (value: boolean, record: any) => record.is_active === value
  },
  {
    title: '最后状态',
    dataIndex: 'last_status',
    key: 'last_status',
    width: 100
  },
  {
    title: '成功/失败',
    key: 'stats',
    width: 120
  },
  {
    title: '超时时间',
    dataIndex: 'timeout_seconds',
    key: 'timeout_seconds',
    width: 100,
    render: (value: number) => `${value}s`
  },
  {
    title: '重试次数',
    dataIndex: 'retry_count',
    key: 'retry_count',
    width: 100
  },
  {
    title: '最后发送',
    dataIndex: 'last_sent_at',
    key: 'last_sent_at',
    width: 180,
    sorter: (a: any, b: any) => {
      if (!a.last_sent_at && !b.last_sent_at) return 0
      if (!a.last_sent_at) return 1
      if (!b.last_sent_at) return -1
      return new Date(a.last_sent_at).getTime() - new Date(b.last_sent_at).getTime()
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
    sorter: (a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

const getShortUrl = (url: string) => {
  if (url.length <= 40) return url
  return url.substring(0, 37) + '...'
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'success': 'green',
    'failed': 'red',
    'timeout': 'orange',
    'error': 'red'
  }
  return colorMap[status.toLowerCase()] || 'default'
}

const handleSearch = () => {
  fetchWebhooks()
}

const handleRefresh = () => {
  fetchWebhooks()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchWebhooks()
}

const handleToggleStatus = async (id: number, is_active: boolean) => {
  toggleLoading.value[id] = true
  try {
    await webhookStore.toggleWebhook(id, is_active)
  } catch (error) {
    console.error('Toggle webhook status error:', error)
  } finally {
    toggleLoading.value[id] = false
  }
}

const handleTest = async (id: number) => {
  try {
    await webhookStore.testWebhook(id)
  } catch (error) {
    console.error('Test webhook error:', error)
  }
}

const handleDelete = async (id: number) => {
  try {
    await webhookStore.deleteWebhook(id)
    fetchWebhooks()
  } catch (error) {
    console.error('Delete webhook error:', error)
  }
}

const fetchWebhooks = async () => {
  const params: WebhookQueryParams = {
    skip: (pagination.current - 1) * pagination.pageSize,
    limit: pagination.pageSize
  }
  
  if (statusFilter.value !== undefined) {
    params.is_active = statusFilter.value
  }
  
  try {
    await webhookStore.fetchWebhooks(params)
    pagination.total = webhookStore.total
  } catch (error) {
    console.error('Fetch webhooks error:', error)
  }
}

onMounted(() => {
  fetchWebhooks()
})
</script>

<style scoped>
.webhook-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.webhook-header h2 {
  margin: 0;
}

.webhook-toolbar {
  margin-bottom: 16px;
}

.webhook-url {
  font-family: monospace;
  font-size: 12px;
}

.webhook-stats {
  display: flex;
  gap: 4px;
}

.text-gray {
  color: #999;
}

:deep(.ant-table-cell) {
  padding: 12px 8px;
}

:deep(.ant-switch-loading) {
  opacity: 0.6;
}
</style>
