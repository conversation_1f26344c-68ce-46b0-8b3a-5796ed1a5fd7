<template>
  <a-menu
    v-model:selectedKeys="selectedKeys"
    mode="inline"
    theme="dark"
    :inline-collapsed="appStore.sidebarCollapsed"
    @click="handleMenuClick"
  >
    <a-menu-item key="/">
      <template #icon>
        <DashboardOutlined />
      </template>
      仪表板
    </a-menu-item>
    
    <a-menu-item key="/project/list">
      <template #icon>
        <ProjectOutlined />
      </template>
      项目管理
    </a-menu-item>
    
    <a-menu-item key="/audit/list">
      <template #icon>
        <BugOutlined />
      </template>
      代码审计
    </a-menu-item>
    
    <a-menu-item key="/vulnerability/list">
      <template #icon>
        <ExclamationCircleOutlined />
      </template>
      漏洞管理
    </a-menu-item>

    <a-menu-item key="/report/list">
      <template #icon>
        <FileTextOutlined />
      </template>
      审计报告
    </a-menu-item>

    <a-menu-item key="/webhook/list">
      <template #icon>
        <ApiOutlined />
      </template>
      Webhook管理
    </a-menu-item>
    
    <a-menu-divider />
    
    <a-menu-item key="/scroll-test">
      <template #icon>
        <ExperimentOutlined />
      </template>
      滚动测试
    </a-menu-item>

    <a-menu-item key="/settings">
      <template #icon>
        <SettingOutlined />
      </template>
      系统设置
    </a-menu-item>
  </a-menu>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import {
  DashboardOutlined,
  ProjectOutlined,
  BugOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  ApiOutlined,
  SettingOutlined,
  ExperimentOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

const selectedKeys = ref<string[]>([route.path])

watch(
  () => route.path,
  (newPath) => {
    selectedKeys.value = [newPath]
  }
)

const handleMenuClick = ({ key }: { key: string }) => {
  if (key !== route.path) {
    router.push(key)
  }
}
</script>

<style scoped>
:deep(.ant-menu-item) {
  margin: 4px 0;
}

:deep(.ant-menu-item-selected) {
  background-color: #1890ff !important;
}
</style>
