<template>
  <div class="report-generate">
    <a-page-header
      title="生成报告"
      sub-title="为审计任务生成详细的安全报告"
      @back="$router.go(-1)"
    />
    
    <a-card>
      <a-form
        ref="formRef"
        :model="generateForm"
        :rules="rules"
        @finish="handleGenerate"
        layout="vertical"
        :style="{ maxWidth: '800px' }"
      >
        <a-form-item label="选择审计" name="audit_id">
          <a-select
            v-model:value="generateForm.audit_id"
            placeholder="请选择要生成报告的审计任务"
            size="large"
            show-search
            :filter-option="filterAuditOption"
            @change="handleAuditChange"
          >
            <a-select-option 
              v-for="audit in audits" 
              :key="audit.id" 
              :value="audit.id"
            >
              <div class="audit-option">
                <div class="audit-title">
                  审计 #{{ audit.id }}
                  <a-tag 
                    :color="getAuditStatusColor(audit.status)" 
                    size="small"
                    style="margin-left: 8px"
                  >
                    {{ getAuditStatusText(audit.status) }}
                  </a-tag>
                </div>
                <div class="audit-info">
                  <span>{{ audit.project_name || `项目 #${audit.project_id}` }}</span>
                  <span class="audit-date">{{ formatDate(audit.created_at) }}</span>
                </div>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="报告格式" name="format">
          <a-radio-group v-model:value="generateForm.format" size="large">
            <a-radio-button value="markdown">
              <div class="format-option">
                <div class="format-title">
                  <FileMarkdownOutlined style="margin-right: 8px;" />
                  Markdown
                </div>
                <div class="format-desc">适合在线查看和编辑的文本格式</div>
              </div>
            </a-radio-button>
            <a-radio-button value="pdf">
              <div class="format-option">
                <div class="format-title">
                  <FilePdfOutlined style="margin-right: 8px;" />
                  PDF
                </div>
                <div class="format-desc">适合打印和正式分发的文档格式</div>
              </div>
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="报告模板" name="template">
          <a-select
            v-model:value="generateForm.template"
            placeholder="选择报告模板（可选）"
            size="large"
            allow-clear
          >
            <a-select-option value="standard">标准模板</a-select-option>
            <a-select-option value="detailed">详细模板</a-select-option>
            <a-select-option value="executive">执行摘要模板</a-select-option>
            <a-select-option value="technical">技术详情模板</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="包含章节" name="include_sections">
          <a-checkbox-group v-model:value="generateForm.include_sections">
            <a-row>
              <a-col :span="12">
                <a-checkbox value="summary">执行摘要</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="methodology">审计方法</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="findings">发现详情</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="recommendations">修复建议</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="statistics">统计图表</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="appendix">附录</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
        
        <!-- 审计信息预览 -->
        <a-form-item v-if="selectedAudit">
          <a-card size="small" title="审计信息预览">
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="审计ID">
                #{{ selectedAudit.id }}
              </a-descriptions-item>
              <a-descriptions-item label="审计类型">
                <a-tag :color="getAuditTypeColor(selectedAudit.type)">
                  {{ getAuditTypeText(selectedAudit.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="项目名称">
                {{ selectedAudit.project_name || `项目 #${selectedAudit.project_id}` }}
              </a-descriptions-item>
              <a-descriptions-item label="审计状态">
                <a-tag :color="getAuditStatusColor(selectedAudit.status)">
                  {{ getAuditStatusText(selectedAudit.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="漏洞数量">
                {{ selectedAudit.vulnerability_count || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">
                {{ formatDate(selectedAudit.created_at) }}
              </a-descriptions-item>
              <a-descriptions-item label="审计描述" :span="2" v-if="selectedAudit.description">
                {{ selectedAudit.description }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-form-item>
        
        <a-form-item label="自定义选项" name="custom_options">
          <a-textarea
            v-model:value="customOptionsText"
            placeholder="请输入JSON格式的自定义选项（可选）"
            :rows="4"
            size="large"
          />
          <template #extra>
            <span class="text-gray">
              JSON格式的自定义配置，如：{"include_code_snippets": true, "max_findings": 50}
            </span>
          </template>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button 
              type="primary" 
              html-type="submit" 
              size="large"
              :loading="reportStore.loading"
            >
              <template #icon>
                <FileTextOutlined />
              </template>
              生成报告
            </a-button>
            <a-button 
              size="large" 
              @click="handlePreview"
              :loading="previewLoading"
              :disabled="!generateForm.audit_id || !generateForm.format"
            >
              <template #icon>
                <EyeOutlined />
              </template>
              预览报告
            </a-button>
            <a-button size="large" @click="$router.go(-1)">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- 预览模态框 -->
    <a-modal
      v-model:open="showPreviewModal"
      title="报告预览"
      width="80%"
      :footer="null"
      :body-style="{ maxHeight: '70vh', overflow: 'auto' }"
    >
      <div v-if="previewContent" class="report-preview">
        <div 
          v-if="generateForm.format === 'markdown'"
          class="markdown-content"
          v-html="renderMarkdown(previewContent)"
        ></div>
        <div v-else class="raw-content">
          <pre>{{ previewContent }}</pre>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useReportStore } from '@/stores/report'
import { useAuditStore } from '@/stores/audit'
import { formatDate } from '@/utils/date'
import { marked } from 'marked'
import { 
  FileMarkdownOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'
import type { ReportGenerateRequest, ReportFormat } from '@/types/report'
import type { AuditStatus, AuditType } from '@/types/audit'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

const router = useRouter()
const reportStore = useReportStore()
const auditStore = useAuditStore()
const formRef = ref<FormInstance>()

const customOptionsText = ref('')
const previewLoading = ref(false)
const showPreviewModal = ref(false)
const previewContent = ref('')

const generateForm = reactive<Omit<ReportGenerateRequest, 'audit_id'> & { audit_id?: number }>({
  audit_id: undefined,
  format: 'markdown',
  template: 'standard',
  include_sections: ['summary', 'findings', 'recommendations', 'statistics'],
  custom_options: {}
})

const audits = computed(() => auditStore.audits.filter(audit => audit.status === 'completed'))
const selectedAudit = computed(() => 
  audits.value.find(audit => audit.id === generateForm.audit_id)
)

const rules = {
  audit_id: [
    { required: true, message: '请选择审计任务', trigger: 'change' }
  ],
  format: [
    { required: true, message: '请选择报告格式', trigger: 'change' }
  ],
  custom_options: [
    { 
      validator: (_: any, value: any) => {
        if (customOptionsText.value) {
          try {
            JSON.parse(customOptionsText.value)
          } catch (error) {
            return Promise.reject(new Error('自定义选项必须是有效的JSON格式'))
          }
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ]
}

const getAuditStatusColor = (status: AuditStatus) => {
  const colorMap: Record<AuditStatus, string> = {
    pending: 'orange',
    running: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colorMap[status] || 'default'
}

const getAuditStatusText = (status: AuditStatus) => {
  const textMap: Record<AuditStatus, string> = {
    pending: '待处理',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

const getAuditTypeColor = (type: AuditType) => {
  const colorMap: Record<AuditType, string> = {
    initial: 'blue',
    update: 'green'
  }
  return colorMap[type] || 'default'
}

const getAuditTypeText = (type: AuditType) => {
  const textMap: Record<AuditType, string> = {
    initial: '初始审计',
    update: '更新审计'
  }
  return textMap[type] || type
}

const filterAuditOption = (input: string, option: any) => {
  const audit = audits.value.find(a => a.id === option.value)
  if (!audit) return false
  
  const searchText = input.toLowerCase()
  return (
    audit.id.toString().includes(searchText) ||
    (audit.project_name && audit.project_name.toLowerCase().includes(searchText)) ||
    (audit.description && audit.description.toLowerCase().includes(searchText))
  )
}

const renderMarkdown = (content: string) => {
  try {
    return marked(content)
  } catch (error) {
    console.error('Markdown render error:', error)
    return `<pre>${content}</pre>`
  }
}

const handleAuditChange = (auditId: number) => {
  // 可以根据审计信息自动推荐报告配置
  const audit = audits.value.find(a => a.id === auditId)
  if (audit) {
    // 根据漏洞数量推荐模板
    if (audit.vulnerability_count && audit.vulnerability_count > 20) {
      generateForm.template = 'detailed'
    } else if (audit.vulnerability_count && audit.vulnerability_count < 5) {
      generateForm.template = 'executive'
    }
  }
}

const handlePreview = async () => {
  if (!generateForm.audit_id || !generateForm.format) {
    message.warning('请先选择审计任务和报告格式')
    return
  }

  previewLoading.value = true
  try {
    const response = await reportStore.previewReport(
      generateForm.audit_id, 
      generateForm.format
    )
    previewContent.value = response.data.content
    showPreviewModal.value = true
  } catch (error) {
    console.error('Preview report error:', error)
  } finally {
    previewLoading.value = false
  }
}

const handleGenerate = async () => {
  if (!generateForm.audit_id) return

  try {
    // 处理自定义选项
    if (customOptionsText.value) {
      try {
        generateForm.custom_options = JSON.parse(customOptionsText.value)
      } catch (error) {
        message.error('自定义选项格式错误，请输入有效的JSON')
        return
      }
    } else {
      generateForm.custom_options = {}
    }
    
    await reportStore.generateReport(generateForm.audit_id, {
      format: generateForm.format,
      template: generateForm.template,
      include_sections: generateForm.include_sections,
      custom_options: generateForm.custom_options
    })
    
    router.push('/report/list')
  } catch (error) {
    console.error('Generate report error:', error)
  }
}

onMounted(async () => {
  // 获取已完成的审计列表
  await auditStore.fetchAudits()
})
</script>

<style scoped>
.report-generate {
  max-width: 1000px;
  margin: 0 auto;
}

.audit-option {
  padding: 4px 0;
}

.audit-title {
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.audit-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.audit-date {
  color: #999;
}

.format-option {
  padding: 8px 12px;
  text-align: left;
}

.format-title {
  font-weight: 500;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.format-desc {
  font-size: 12px;
  color: #666;
}

.text-gray {
  color: #666;
  font-size: 12px;
}

.report-preview {
  padding: 0;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3) {
  margin-top: 24px;
  margin-bottom: 16px;
  color: #262626;
}

.markdown-content :deep(h1) {
  font-size: 24px;
  border-bottom: 1px solid #d9d9d9;
  padding-bottom: 8px;
}

.markdown-content :deep(h2) {
  font-size: 20px;
}

.markdown-content :deep(h3) {
  font-size: 16px;
}

.markdown-content :deep(p) {
  margin-bottom: 16px;
}

.markdown-content :deep(code) {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.markdown-content :deep(pre) {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 16px 0;
}

.raw-content pre {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 12px;
  line-height: 1.4;
}

:deep(.ant-radio-button-wrapper) {
  height: auto;
  padding: 0;
  border-radius: 6px;
}

:deep(.ant-radio-button-wrapper:not(:first-child)::before) {
  display: none;
}

:deep(.ant-radio-button-wrapper:first-child) {
  border-radius: 6px 0 0 6px;
}

:deep(.ant-radio-button-wrapper:last-child) {
  border-radius: 0 6px 6px 0;
}

:deep(.ant-form-item-extra) {
  margin-top: 4px;
}

:deep(.ant-checkbox-group .ant-col) {
  margin-bottom: 8px;
}
</style>
