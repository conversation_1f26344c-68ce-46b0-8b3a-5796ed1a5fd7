import api from './index'
import type { 
  Webhook,
  WebhookCreateRequest,
  WebhookUpdateRequest,
  WebhookEventResponse,
  WebhookEventsListResponse,
  WebhookQueryParams,
  WebhookTestRequest,
  WebhookTestResponse
} from '@/types/webhook'

export const webhookApi = {
  // 获取Webhook列表
  getWebhooks: (params?: WebhookQueryParams) => 
    api.get<Webhook[]>('/api/v1/webhooks/', { params }),

  // 获取Webhook详情
  getWebhook: (id: number) => 
    api.get<Webhook>(`/api/v1/webhooks/${id}`),

  // 创建Webhook
  createWebhook: (data: WebhookCreateRequest) => 
    api.post<Webhook>('/api/v1/webhooks/', { webhook_data: data }),

  // 更新Webhook
  updateWebhook: (id: number, data: WebhookUpdateRequest) => 
    api.put<Webhook>(`/api/v1/webhooks/${id}`, { webhook_data: data }),

  // 删除Webhook
  deleteWebhook: (id: number) => 
    api.delete(`/api/v1/webhooks/${id}`),

  // 测试Webhook
  testWebhook: (id: number, data?: WebhookTestRequest) => 
    api.post<WebhookTestResponse>(`/api/v1/webhooks/${id}/test`, data || {}),

  // 获取Webhook事件历史
  getWebhookEvents: (id: number, params?: { skip?: number; limit?: number }) => 
    api.get<WebhookEventResponse[]>(`/api/v1/webhooks/${id}/events`, { params }),

  // 获取可用事件类型
  getAvailableEvents: () => 
    api.get<WebhookEventsListResponse>('/api/v1/webhooks/events'),

  // 启用/禁用Webhook
  toggleWebhook: (id: number, is_active: boolean) => 
    api.put<Webhook>(`/api/v1/webhooks/${id}`, { 
      webhook_data: { is_active } 
    }),
}
