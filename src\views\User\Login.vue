<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="/logo.svg" alt="VulnAuditBox" class="logo" />
        <h1>VulnAuditBox</h1>
        <p>AI驱动的代码审计平台</p>
      </div>
      
      <a-form
        ref="formRef"
        :model="loginForm"
        :rules="rules"
        @finish="handleLogin"
        class="login-form"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username">
          <a-input
            v-model:value="loginForm.username"
            size="large"
            placeholder="请输入用户名或邮箱"
            :prefix="h(UserOutlined)"
          />
        </a-form-item>
        
        <a-form-item label="密码" name="password">
          <a-input-password
            v-model:value="loginForm.password"
            size="large"
            placeholder="请输入密码"
            :prefix="h(LockOutlined)"
          />
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="userStore.loading"
          >
            登录
          </a-button>
        </a-form-item>
        
        <div class="login-footer">
          <span>还没有账号？</span>
          <router-link to="/user/register">立即注册</router-link>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, h } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import type { LoginForm } from '@/types/user'
import type { FormInstance } from 'ant-design-vue'

const router = useRouter()
const userStore = useUserStore()
const formRef = ref<FormInstance>()

const loginForm = reactive<LoginForm>({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于8位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  try {
    await userStore.login(loginForm)
    router.push('/')
  } catch (error) {
    // 错误已在store中处理
    console.error('Login error:', error)
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  height: 64px;
  margin-bottom: 16px;
}

.login-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.login-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.login-form {
  margin-top: 32px;
}

.login-footer {
  text-align: center;
  margin-top: 16px;
  color: #8c8c8c;
}

.login-footer a {
  color: #1890ff;
  text-decoration: none;
  margin-left: 4px;
}

.login-footer a:hover {
  text-decoration: underline;
}
</style>
