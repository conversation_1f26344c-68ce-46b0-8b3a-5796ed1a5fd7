<template>
  <div class="webhook-index">
    <a-card title="Webhook管理">
      <p>这是Webhook管理的入口页面，包含以下功能：</p>
      <a-space direction="vertical" size="middle">
        <router-link to="/webhook/list">
          <a-button type="primary" size="large">
            <template #icon>
              <ApiOutlined />
            </template>
            Webhook列表
          </a-button>
        </router-link>
        <router-link to="/webhook/create">
          <a-button type="default" size="large">
            <template #icon>
              <PlusOutlined />
            </template>
            创建Webhook
          </a-button>
        </router-link>
      </a-space>
      
      <a-divider />
      
      <div class="feature-list">
        <h3>功能特性</h3>
        <ul>
          <li>🔗 支持HTTP/HTTPS Webhook URL</li>
          <li>📡 多事件类型监听和订阅</li>
          <li>🔒 HMAC签名验证支持</li>
          <li>⚙️ 自定义HTTP请求头</li>
          <li>🔄 失败重试机制</li>
          <li>⏱️ 可配置超时时间</li>
          <li>📊 事件发送统计和历史</li>
          <li>🧪 Webhook测试功能</li>
          <li>🎛️ 启用/禁用状态控制</li>
        </ul>
      </div>
      
      <a-divider />
      
      <div class="webhook-info">
        <h3>Webhook说明</h3>
        <a-alert
          message="什么是Webhook？"
          description="Webhook是一种HTTP回调机制，当系统中发生特定事件时，会自动向您配置的URL发送POST请求，实现实时通知功能。"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        />
        
        <h4>支持的事件类型</h4>
        <a-space wrap>
          <a-tag color="blue">audit.created</a-tag>
          <a-tag color="blue">audit.completed</a-tag>
          <a-tag color="blue">audit.failed</a-tag>
          <a-tag color="green">vulnerability.found</a-tag>
          <a-tag color="green">vulnerability.updated</a-tag>
          <a-tag color="orange">report.generated</a-tag>
          <a-tag color="purple">project.created</a-tag>
          <a-tag color="purple">project.updated</a-tag>
        </a-space>
        
        <h4 style="margin-top: 16px">请求格式</h4>
        <pre class="webhook-example">
POST /your-webhook-endpoint
Content-Type: application/json
X-VulnAuditBox-Signature: sha256=...
X-VulnAuditBox-Event: audit.completed

{
  "event": "audit.completed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "audit_id": 123,
    "project_id": 456,
    "status": "completed",
    "vulnerability_count": 5
  }
}
        </pre>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ApiOutlined, PlusOutlined } from '@ant-design/icons-vue'
</script>

<style scoped>
.webhook-index {
  max-width: 800px;
  margin: 0 auto;
}

.feature-list {
  margin-top: 16px;
}

.feature-list h3 {
  margin-bottom: 12px;
  color: #262626;
}

.feature-list ul {
  list-style: none;
  padding: 0;
}

.feature-list li {
  padding: 4px 0;
  font-size: 14px;
  color: #595959;
}

.webhook-info h3,
.webhook-info h4 {
  color: #262626;
  margin-bottom: 12px;
}

.webhook-example {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  border-left: 4px solid #1890ff;
}
</style>
