<template>
  <div class="report-index">
    <a-card title="报告管理">
      <p>这是报告管理的入口页面，包含以下功能：</p>
      <a-space direction="vertical" size="middle">
        <router-link to="/report/list">
          <a-button type="primary" size="large">
            <template #icon>
              <FileTextOutlined />
            </template>
            报告列表
          </a-button>
        </router-link>
        <router-link to="/report/generate">
          <a-button type="default" size="large">
            <template #icon>
              <PlusOutlined />
            </template>
            生成报告
          </a-button>
        </router-link>
      </a-space>
      
      <a-divider />
      
      <div class="feature-list">
        <h3>功能特性</h3>
        <ul>
          <li>📊 支持多种报告格式（Markdown、PDF）</li>
          <li>📋 报告列表管理和筛选</li>
          <li>👁️ 在线预览Markdown报告</li>
          <li>📥 一键下载报告文件</li>
          <li>🔔 通知状态管理</li>
          <li>📈 报告统计分析</li>
          <li>🎨 多种报告模板选择</li>
          <li>⚙️ 自定义报告配置</li>
        </ul>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { FileTextOutlined, PlusOutlined } from '@ant-design/icons-vue'
</script>

<style scoped>
.report-index {
  max-width: 600px;
  margin: 0 auto;
}

.feature-list {
  margin-top: 16px;
}

.feature-list h3 {
  margin-bottom: 12px;
  color: #262626;
}

.feature-list ul {
  list-style: none;
  padding: 0;
}

.feature-list li {
  padding: 4px 0;
  font-size: 14px;
  color: #595959;
}
</style>
