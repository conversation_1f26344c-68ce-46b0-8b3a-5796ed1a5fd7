<template>
  <div class="scroll-test">
    <a-card title="滚动测试页面" :bordered="false">
      <p>这是一个用于测试滚动效果的页面。头部栏、底部栏和侧边栏应该保持固定，只有这个内容区域可以滚动。</p>
      
      <a-divider>测试内容区域</a-divider>
      
      <div v-for="i in 50" :key="i" class="test-item">
        <a-card :title="`测试卡片 ${i}`" size="small" style="margin-bottom: 16px;">
          <p>这是第 {{ i }} 个测试卡片的内容。</p>
          <p>当页面内容超过视窗高度时，应该只有中间的内容区域出现滚动条。</p>
          <p>头部导航栏、侧边栏和底部栏应该始终保持在固定位置，不参与滚动。</p>
          
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="数值 A" :value="i * 10" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="数值 B" :value="i * 20" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="数值 C" :value="i * 30" />
            </a-col>
          </a-row>
          
          <a-divider />
          
          <a-space>
            <a-button type="primary" size="small">操作按钮</a-button>
            <a-button size="small">查看详情</a-button>
            <a-button danger size="small">删除</a-button>
          </a-space>
        </a-card>
      </div>
      
      <a-divider>页面底部</a-divider>
      <p>如果你能看到这段文字，说明滚动功能正常工作。</p>
    </a-card>
  </div>
</template>

<script setup lang="ts">
// 这是一个简单的测试页面，不需要复杂的逻辑
</script>

<style scoped>
.scroll-test {
  padding: 24px;
}

.test-item {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
