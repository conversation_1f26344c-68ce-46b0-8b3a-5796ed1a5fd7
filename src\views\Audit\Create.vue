<template>
  <div class="audit-create">
    <a-page-header
      title="创建审计任务"
      sub-title="为项目创建新的代码审计任务"
      @back="$router.go(-1)"
    />
    
    <a-card>
      <a-form
        ref="formRef"
        :model="auditForm"
        :rules="rules"
        @finish="handleSubmit"
        layout="vertical"
        :style="{ maxWidth: '600px' }"
      >
        <a-form-item label="选择项目" name="project_id">
          <a-select
            v-model:value="auditForm.project_id"
            placeholder="请选择要审计的项目"
            size="large"
            show-search
            :filter-option="filterProjectOption"
            @change="handleProjectChange"
          >
            <a-select-option 
              v-for="project in projects" 
              :key="project.id" 
              :value="project.id"
            >
              <div class="project-option">
                <div class="project-name">{{ project.name }}</div>
                <div class="project-info">
                  <a-tag size="small" :color="getProjectTypeColor(project.type)">
                    {{ getProjectTypeText(project.type) }}
                  </a-tag>
                  <span class="project-url">{{ getProjectUrl(project) }}</span>
                </div>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="审计类型" name="type">
          <a-radio-group v-model:value="auditForm.type" size="large">
            <a-radio-button value="initial">
              <div class="audit-type-option">
                <div class="type-title">初始审计</div>
                <div class="type-desc">对项目进行完整的安全审计</div>
              </div>
            </a-radio-button>
            <a-radio-button value="update">
              <div class="audit-type-option">
                <div class="type-title">更新审计</div>
                <div class="type-desc">仅审计项目的变更部分</div>
              </div>
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="审计描述" name="description">
          <a-textarea
            v-model:value="auditForm.description"
            placeholder="请输入审计任务的描述信息（可选）"
            :rows="4"
            size="large"
          />
          <template #extra>
            <span class="text-gray">描述此次审计的目的、范围或特殊要求</span>
          </template>
        </a-form-item>
        
        <!-- 项目信息预览 -->
        <a-form-item v-if="selectedProject">
          <a-card size="small" title="项目信息预览">
            <a-descriptions :column="1" size="small">
              <a-descriptions-item label="项目名称">
                {{ selectedProject.name }}
              </a-descriptions-item>
              <a-descriptions-item label="项目类型">
                <a-tag :color="getProjectTypeColor(selectedProject.type)">
                  {{ getProjectTypeText(selectedProject.type) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="仓库/镜像" v-if="getProjectUrl(selectedProject)">
                {{ getProjectUrl(selectedProject) }}
              </a-descriptions-item>
              <a-descriptions-item label="源码路径" v-if="selectedProject.source_path">
                {{ selectedProject.source_path }}
              </a-descriptions-item>
              <a-descriptions-item label="当前哈希" v-if="selectedProject.current_hash">
                <a-typography-text 
                  :copyable="{ text: selectedProject.current_hash }"
                  style="font-family: monospace; font-size: 12px;"
                >
                  {{ selectedProject.current_hash }}
                </a-typography-text>
              </a-descriptions-item>
              <a-descriptions-item label="项目描述" v-if="selectedProject.description">
                {{ selectedProject.description }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button 
              type="primary" 
              html-type="submit" 
              size="large"
              :loading="auditStore.loading"
            >
              创建审计任务
            </a-button>
            <a-button size="large" @click="$router.go(-1)">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuditStore } from '@/stores/audit'
import { useProjectStore } from '@/stores/project'
import type { CreateAuditForm, AuditType } from '@/types/audit'
import type { Project, ProjectType } from '@/types/project'
import type { FormInstance } from 'ant-design-vue'

const router = useRouter()
const auditStore = useAuditStore()
const projectStore = useProjectStore()
const formRef = ref<FormInstance>()

const auditForm = reactive<CreateAuditForm>({
  project_id: undefined as any,
  type: 'initial',
  description: ''
})

const projects = computed(() => projectStore.projects)
const selectedProject = computed(() => 
  projects.value.find(p => p.id === auditForm.project_id)
)

const rules = {
  project_id: [
    { required: true, message: '请选择要审计的项目', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择审计类型', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ]
}

const getProjectTypeColor = (type: ProjectType) => {
  const colorMap: Record<ProjectType, string> = {
    git: 'blue',
    docker: 'green'
  }
  return colorMap[type] || 'default'
}

const getProjectTypeText = (type: ProjectType) => {
  const textMap: Record<ProjectType, string> = {
    git: 'Git项目',
    docker: 'Docker项目'
  }
  return textMap[type] || type
}

const getProjectUrl = (project: Project) => {
  if (project.type === 'git') {
    return project.git_url
  } else if (project.type === 'docker') {
    return project.docker_image
  }
  return ''
}

const filterProjectOption = (input: string, option: any) => {
  const project = projects.value.find(p => p.id === option.value)
  if (!project) return false
  
  const searchText = input.toLowerCase()
  return (
    project.name.toLowerCase().includes(searchText) ||
    (project.git_url && project.git_url.toLowerCase().includes(searchText)) ||
    (project.docker_image && project.docker_image.toLowerCase().includes(searchText))
  )
}

const handleProjectChange = (projectId: number) => {
  // 可以根据项目类型自动推荐审计类型
  const project = projects.value.find(p => p.id === projectId)
  if (project && project.current_hash) {
    // 如果项目有当前哈希，可能适合更新审计
    // 这里可以添加更智能的推荐逻辑
  }
}

const handleSubmit = async () => {
  try {
    await auditStore.createAudit(auditForm)
    router.push('/audit/list')
  } catch (error) {
    console.error('Create audit error:', error)
  }
}

onMounted(async () => {
  // 获取项目列表
  await projectStore.fetchProjects()
})
</script>

<style scoped>
.audit-create {
  max-width: 800px;
  margin: 0 auto;
}

.project-option {
  padding: 4px 0;
}

.project-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.project-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.project-url {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.audit-type-option {
  padding: 8px 12px;
  text-align: left;
}

.type-title {
  font-weight: 500;
  margin-bottom: 2px;
}

.type-desc {
  font-size: 12px;
  color: #666;
}

.text-gray {
  color: #666;
  font-size: 12px;
}

:deep(.ant-radio-button-wrapper) {
  height: auto;
  padding: 0;
  border-radius: 6px;
}

:deep(.ant-radio-button-wrapper:not(:first-child)::before) {
  display: none;
}

:deep(.ant-radio-button-wrapper:first-child) {
  border-radius: 6px 0 0 6px;
}

:deep(.ant-radio-button-wrapper:last-child) {
  border-radius: 0 6px 6px 0;
}

:deep(.ant-form-item-extra) {
  margin-top: 4px;
}
</style>
