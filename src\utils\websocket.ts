import { ref, reactive } from 'vue'
import { getToken } from './auth'
import type { 
  WebSocketMessage, 
  AuditProgressMessage, 
  AuditStatusMessage, 
  AuditLogMessage 
} from '@/types/audit'

export interface WebSocketOptions {
  url?: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
}

export class WebSocketManager {
  private ws: WebSocket | null = null
  private url: string
  private reconnectInterval: number
  private maxReconnectAttempts: number
  private heartbeatInterval: number
  private reconnectAttempts = 0
  private heartbeatTimer: number | null = null
  private reconnectTimer: number | null = null

  public isConnected = ref(false)
  public isConnecting = ref(false)
  public error = ref<string | null>(null)

  private listeners = reactive<{
    [key: string]: Array<(data: any) => void>
  }>({})

  constructor(options: WebSocketOptions = {}) {
    this.url = options.url || `ws://localhost:8080/ws`
    this.reconnectInterval = options.reconnectInterval || 3000
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5
    this.heartbeatInterval = options.heartbeatInterval || 30000
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      this.isConnecting.value = true
      this.error.value = null

      const token = getToken()
      const wsUrl = token ? `${this.url}?token=${token}` : this.url

      try {
        this.ws = new WebSocket(wsUrl)

        this.ws.onopen = () => {
          console.log('WebSocket connected')
          this.isConnected.value = true
          this.isConnecting.value = false
          this.reconnectAttempts = 0
          this.startHeartbeat()
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error)
          }
        }

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason)
          this.isConnected.value = false
          this.isConnecting.value = false
          this.stopHeartbeat()

          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error)
          this.error.value = 'WebSocket连接错误'
          this.isConnecting.value = false
          reject(error)
        }
      } catch (error) {
        this.isConnecting.value = false
        this.error.value = '无法创建WebSocket连接'
        reject(error)
      }
    })
  }

  disconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    this.stopHeartbeat()

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }

    this.isConnected.value = false
    this.isConnecting.value = false
    this.reconnectAttempts = 0
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  // 订阅消息
  on(type: string, callback: (data: any) => void) {
    if (!this.listeners[type]) {
      this.listeners[type] = []
    }
    this.listeners[type].push(callback)
  }

  // 取消订阅
  off(type: string, callback?: (data: any) => void) {
    if (!this.listeners[type]) return

    if (callback) {
      const index = this.listeners[type].indexOf(callback)
      if (index > -1) {
        this.listeners[type].splice(index, 1)
      }
    } else {
      this.listeners[type] = []
    }
  }

  // 订阅审计进度更新
  onAuditProgress(callback: (data: AuditProgressMessage) => void) {
    this.on('audit_progress', callback)
  }

  // 订阅审计状态更新
  onAuditStatus(callback: (data: AuditStatusMessage) => void) {
    this.on('audit_status', callback)
  }

  // 订阅审计日志
  onAuditLog(callback: (data: AuditLogMessage) => void) {
    this.on('audit_log', callback)
  }

  private handleMessage(message: WebSocketMessage) {
    const { type, data } = message
    
    if (this.listeners[type]) {
      this.listeners[type].forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Error in WebSocket ${type} handler:`, error)
        }
      })
    }
  }

  private startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatTimer = window.setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'ping' })
      }
    }, this.heartbeatInterval)
  }

  private stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  private scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    this.reconnectAttempts++
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`)

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnect failed:', error)
      })
    }, this.reconnectInterval)
  }
}

// 全局WebSocket实例
export const websocketManager = new WebSocketManager()

// 便捷方法
export const connectWebSocket = () => websocketManager.connect()
export const disconnectWebSocket = () => websocketManager.disconnect()
export const isWebSocketConnected = () => websocketManager.isConnected
export const isWebSocketConnecting = () => websocketManager.isConnecting
export const getWebSocketError = () => websocketManager.error
