// 基于API文档的报告类型定义
export type ReportFormat = 'markdown' | 'pdf'

export interface Report {
  id: number
  audit_id: number
  format: ReportFormat
  content: string
  generated_at: string
  notified: boolean
  deleted_at?: string | null
  extra_data?: Record<string, any> | null
  // 关联数据
  audit_name?: string
  project_name?: string
  project_id?: number
}

export interface ReportCreate {
  audit_id: number
  format: ReportFormat
  content: string
}

export interface ReportUpdate {
  content?: string | null
  notified?: boolean | null
  extra_data?: Record<string, any> | null
}

export interface ReportGenerateRequest {
  audit_id: number
  format: ReportFormat
  template?: string
  include_sections?: string[]
  custom_options?: Record<string, any>
}

export interface ReportQueryParams {
  audit_id?: number
  format?: ReportFormat
  notified?: boolean
  skip?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface ReportStats {
  total_reports: number
  by_format: Record<ReportFormat, number>
  recent_generated: number
  pending_notification: number
}

// 报告预览数据
export interface ReportPreview {
  title: string
  summary: string
  sections: ReportSection[]
  metadata: ReportMetadata
}

export interface ReportSection {
  title: string
  content: string
  type: 'text' | 'table' | 'chart' | 'code'
  data?: any
}

export interface ReportMetadata {
  generated_at: string
  audit_id: number
  project_name: string
  vulnerability_count: number
  severity_breakdown: Record<string, number>
}
