import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { theme } from 'ant-design-vue'

export type ThemeMode = 'light' | 'dark' | 'auto'

export interface ThemeConfig {
  mode: ThemeMode
  primaryColor: string
  borderRadius: number
  compactMode: boolean
  colorWeakMode: boolean
}

const THEME_STORAGE_KEY = 'vulnauditbox_theme_config'

// 默认主题配置
const defaultThemeConfig: ThemeConfig = {
  mode: 'light',
  primaryColor: '#1890ff',
  borderRadius: 6,
  compactMode: false,
  colorWeakMode: false
}

export const useThemeStore = defineStore('theme', () => {
  const themeConfig = ref<ThemeConfig>({ ...defaultThemeConfig })
  const systemTheme = ref<'light' | 'dark'>('light')

  // 计算当前实际主题
  const currentTheme = computed(() => {
    if (themeConfig.value.mode === 'auto') {
      return systemTheme.value
    }
    return themeConfig.value.mode
  })

  // 是否为暗黑模式
  const isDarkMode = computed(() => currentTheme.value === 'dark')

  // Ant Design 主题配置
  const antdThemeConfig = computed(() => {
    const baseConfig = {
      token: {
        colorPrimary: themeConfig.value.primaryColor,
        borderRadius: themeConfig.value.borderRadius,
        // 紧凑模式
        ...(themeConfig.value.compactMode && {
          sizeStep: 3,
          sizeUnit: 3,
          controlHeight: 28
        }),
        // 色弱模式
        ...(themeConfig.value.colorWeakMode && {
          colorError: '#d48806',
          colorWarning: '#d48806',
          colorSuccess: '#389e0d'
        })
      },
      components: {
        Layout: {
          bodyBg: isDarkMode.value ? '#141414' : '#f0f2f5',
          headerBg: isDarkMode.value ? '#1f1f1f' : '#ffffff',
          siderBg: isDarkMode.value ? '#1f1f1f' : '#ffffff'
        },
        Menu: {
          itemBg: isDarkMode.value ? '#1f1f1f' : '#ffffff',
          itemSelectedBg: isDarkMode.value ? '#40a9ff' : '#e6f7ff',
          itemHoverBg: isDarkMode.value ? '#1890ff' : '#f5f5f5'
        },
        Card: {
          colorBgContainer: isDarkMode.value ? '#1f1f1f' : '#ffffff'
        },
        Table: {
          headerBg: isDarkMode.value ? '#262626' : '#fafafa',
          rowHoverBg: isDarkMode.value ? '#262626' : '#f5f5f5'
        }
      }
    }

    // 应用暗黑主题算法
    if (isDarkMode.value) {
      return {
        ...baseConfig,
        algorithm: theme.darkAlgorithm
      }
    }

    return {
      ...baseConfig,
      algorithm: theme.defaultAlgorithm
    }
  })

  // 预设主题颜色
  const presetColors = [
    { name: '拂晓蓝', value: '#1890ff' },
    { name: '薄暮', value: '#722ed1' },
    { name: '火山', value: '#fa541c' },
    { name: '日暮', value: '#faad14' },
    { name: '明青', value: '#13c2c2' },
    { name: '极光绿', value: '#52c41a' },
    { name: '极客蓝', value: '#2f54eb' },
    { name: '酱紫', value: '#722ed1' }
  ]

  // 加载主题配置
  const loadThemeConfig = () => {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY)
      if (stored) {
        const config = JSON.parse(stored)
        themeConfig.value = { ...defaultThemeConfig, ...config }
      }
    } catch (error) {
      console.error('Failed to load theme config:', error)
      themeConfig.value = { ...defaultThemeConfig }
    }
  }

  // 保存主题配置
  const saveThemeConfig = () => {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(themeConfig.value))
    } catch (error) {
      console.error('Failed to save theme config:', error)
    }
  }

  // 设置主题模式
  const setThemeMode = (mode: ThemeMode) => {
    themeConfig.value.mode = mode
    saveThemeConfig()
    applyTheme()
  }

  // 设置主色调
  const setPrimaryColor = (color: string) => {
    themeConfig.value.primaryColor = color
    saveThemeConfig()
    applyTheme()
  }

  // 设置圆角大小
  const setBorderRadius = (radius: number) => {
    themeConfig.value.borderRadius = Math.max(0, Math.min(16, radius))
    saveThemeConfig()
    applyTheme()
  }

  // 切换紧凑模式
  const toggleCompactMode = () => {
    themeConfig.value.compactMode = !themeConfig.value.compactMode
    saveThemeConfig()
    applyTheme()
  }

  // 切换色弱模式
  const toggleColorWeakMode = () => {
    themeConfig.value.colorWeakMode = !themeConfig.value.colorWeakMode
    saveThemeConfig()
    applyTheme()
  }

  // 重置主题配置
  const resetThemeConfig = () => {
    themeConfig.value = { ...defaultThemeConfig }
    saveThemeConfig()
    applyTheme()
  }

  // 应用主题到DOM
  const applyTheme = () => {
    const root = document.documentElement
    
    // 设置主题模式类名
    root.setAttribute('data-theme', currentTheme.value)
    
    // 设置CSS变量
    root.style.setProperty('--primary-color', themeConfig.value.primaryColor)
    root.style.setProperty('--border-radius', `${themeConfig.value.borderRadius}px`)
    
    // 设置body背景色
    document.body.style.backgroundColor = isDarkMode.value ? '#141414' : '#f0f2f5'
    
    // 色弱模式处理
    if (themeConfig.value.colorWeakMode) {
      root.classList.add('color-weak')
    } else {
      root.classList.remove('color-weak')
    }
  }

  // 监听系统主题变化
  const watchSystemTheme = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = (e: MediaQueryListEvent) => {
        systemTheme.value = e.matches ? 'dark' : 'light'
        if (themeConfig.value.mode === 'auto') {
          applyTheme()
        }
      }
      
      // 初始化系统主题
      systemTheme.value = mediaQuery.matches ? 'dark' : 'light'
      
      // 监听变化
      mediaQuery.addEventListener('change', handleChange)
      
      // 返回清理函数
      return () => {
        mediaQuery.removeEventListener('change', handleChange)
      }
    }
  }

  // 获取主题状态信息
  const getThemeInfo = () => {
    return {
      currentTheme: currentTheme.value,
      isDarkMode: isDarkMode.value,
      config: { ...themeConfig.value },
      systemTheme: systemTheme.value
    }
  }

  // 导出主题配置
  const exportThemeConfig = () => {
    return JSON.stringify(themeConfig.value, null, 2)
  }

  // 导入主题配置
  const importThemeConfig = (configJson: string) => {
    try {
      const config = JSON.parse(configJson)
      themeConfig.value = { ...defaultThemeConfig, ...config }
      saveThemeConfig()
      applyTheme()
      return true
    } catch (error) {
      console.error('Failed to import theme config:', error)
      return false
    }
  }

  // 初始化主题
  const initTheme = () => {
    loadThemeConfig()
    const cleanup = watchSystemTheme()
    applyTheme()
    return cleanup
  }

  return {
    // 状态
    themeConfig,
    systemTheme,
    currentTheme,
    isDarkMode,
    antdThemeConfig,
    presetColors,
    
    // 方法
    setThemeMode,
    setPrimaryColor,
    setBorderRadius,
    toggleCompactMode,
    toggleColorWeakMode,
    resetThemeConfig,
    applyTheme,
    getThemeInfo,
    exportThemeConfig,
    importThemeConfig,
    initTheme,
    
    // 工具方法
    loadThemeConfig,
    saveThemeConfig
  }
})
