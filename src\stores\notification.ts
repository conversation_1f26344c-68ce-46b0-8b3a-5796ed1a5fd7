import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { NotificationItem, NotificationSettings } from '@/types/notification'

export const useNotificationStore = defineStore('notification', () => {
  const notifications = ref<NotificationItem[]>([])
  const settings = ref<NotificationSettings>({
    enableDesktop: true,
    enableSound: true,
    enableEmail: false,
    auditCompleted: true,
    auditFailed: true,
    vulnerabilityFound: true,
    reportGenerated: true,
    webhookFailed: true
  })

  // 计算属性
  const unreadCount = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  // 添加通知
  const addNotification = (notification: Omit<NotificationItem, 'id'>) => {
    const newNotification: NotificationItem = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      read: false
    }
    notifications.value.unshift(newNotification)
    
    // 限制通知数量，最多保留100条
    if (notifications.value.length > 100) {
      notifications.value = notifications.value.slice(0, 100)
    }
  }

  // 标记为已读
  const markAsRead = (id: string) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
      notification.updatedAt = new Date().toISOString()
    }
  }

  // 标记全部已读
  const markAllAsRead = () => {
    const now = new Date().toISOString()
    notifications.value.forEach(notification => {
      if (!notification.read) {
        notification.read = true
        notification.updatedAt = now
      }
    })
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清空所有通知
  const clearAll = () => {
    notifications.value = []
  }

  // 获取通知列表（模拟API调用）
  const fetchNotifications = async () => {
    // 这里应该调用实际的API
    // 目前使用模拟数据
    const mockNotifications: NotificationItem[] = [
      {
        id: '1',
        type: 'audit',
        message: '审计 #123 已完成，发现 5 个漏洞',
        actionUrl: '/audit/detail/123',
        read: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30分钟前
      },
      {
        id: '2',
        type: 'warning',
        message: '发现高危漏洞：SQL注入',
        actionUrl: '/vulnerability/detail/456',
        read: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2小时前
      },
      {
        id: '3',
        type: 'report',
        message: '安全报告 #789 生成完成',
        actionUrl: '/report/list',
        read: true,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1天前
      }
    ]
    
    notifications.value = mockNotifications
  }

  // 更新设置
  const updateSettings = (newSettings: Partial<NotificationSettings>) => {
    settings.value = { ...settings.value, ...newSettings }
    // 这里应该调用API保存设置
  }

  return {
    notifications,
    settings,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    fetchNotifications,
    updateSettings
  }
})
