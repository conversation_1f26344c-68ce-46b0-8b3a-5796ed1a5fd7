import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { 
  Report,
  ReportCreate,
  ReportGenerateRequest,
  ReportQueryParams,
  ReportStats
} from '@/types/report'
import { reportApi } from '@/api/report'
import { message } from 'ant-design-vue'
import { downloadFile } from '@/utils/common'

export const useReportStore = defineStore('report', () => {
  const reports = ref<Report[]>([])
  const currentReport = ref<Report | null>(null)
  const statistics = ref<ReportStats | null>(null)
  const loading = ref(false)
  const total = ref(0)

  // 获取报告列表
  const fetchReports = async (params?: ReportQueryParams) => {
    loading.value = true
    try {
      const response = await reportApi.getReports(params)
      reports.value = response.data
      total.value = response.data.length
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取报告列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取报告详情
  const fetchReport = async (id: number) => {
    loading.value = true
    try {
      const response = await reportApi.getReport(id)
      currentReport.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取报告详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建报告
  const createReport = async (reportForm: ReportCreate) => {
    loading.value = true
    try {
      const response = await reportApi.createReport(reportForm)
      reports.value.unshift(response.data)
      message.success('报告创建成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '创建报告失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 生成报告
  const generateReport = async (auditId: number, data: Omit<ReportGenerateRequest, 'audit_id'>) => {
    loading.value = true
    try {
      const response = await reportApi.generateReport(auditId, data)
      reports.value.unshift(response.data)
      message.success('报告生成成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '生成报告失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 下载报告
  const downloadReport = async (id: number, filename?: string) => {
    loading.value = true
    try {
      const response = await reportApi.downloadReport(id)
      
      // 从响应头获取文件名
      const contentDisposition = response.headers['content-disposition']
      let downloadFilename = filename
      
      if (!downloadFilename && contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          downloadFilename = filenameMatch[1].replace(/['"]/g, '')
        }
      }
      
      if (!downloadFilename) {
        const report = reports.value.find(r => r.id === id)
        const extension = report?.format === 'pdf' ? 'pdf' : 'md'
        downloadFilename = `report_${id}.${extension}`
      }
      
      // 创建下载链接
      const blob = new Blob([response.data])
      downloadFile(blob, downloadFilename)
      
      message.success('报告下载成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '下载报告失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除报告
  const deleteReport = async (id: number) => {
    loading.value = true
    try {
      await reportApi.deleteReport(id)
      reports.value = reports.value.filter(r => r.id !== id)
      
      if (currentReport.value?.id === id) {
        currentReport.value = null
      }
      
      message.success('报告删除成功')
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '删除报告失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 标记为已通知
  const markAsNotified = async (id: number) => {
    try {
      const response = await reportApi.markAsNotified(id)
      
      // 更新本地状态
      const index = reports.value.findIndex(r => r.id === id)
      if (index !== -1) {
        reports.value[index] = response.data
      }
      
      if (currentReport.value?.id === id) {
        currentReport.value = response.data
      }
      
      message.success('已标记为已通知')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '标记通知状态失败')
      throw error
    }
  }

  // 获取报告统计
  const fetchStatistics = async () => {
    try {
      const response = await reportApi.getReportStats()
      statistics.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取报告统计失败')
      throw error
    }
  }

  // 预览报告
  const previewReport = async (auditId: number, format: string) => {
    loading.value = true
    try {
      const response = await reportApi.previewReport(auditId, format)
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '预览报告失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    reports,
    currentReport,
    statistics,
    loading,
    total,
    fetchReports,
    fetchReport,
    createReport,
    generateReport,
    downloadReport,
    deleteReport,
    markAsNotified,
    fetchStatistics,
    previewReport
  }
})
