// 基于API文档的审计类型定义
export type AuditStatus = 'pending' | 'running' | 'completed' | 'failed'
export type AuditType = 'initial' | 'update'

export interface Audit {
  id: number
  type: AuditType
  project_id: number
  status: AuditStatus
  start_time?: string
  end_time?: string
  duration?: number
  description?: string
  diff_content?: string
  created_at: string
  updated_at: string
  // 关联数据
  project_name?: string
  vulnerability_count?: number
}

export interface AuditDetailResponse extends Audit {
  logs: AuditLog[]
  vulnerabilities_summary?: VulnerabilitySummary
}

export interface AuditLog {
  id: number
  audit_id: number
  level: 'info' | 'warning' | 'error'
  message: string
  timestamp: string
}

export interface AuditProgress {
  audit_id: number
  status: AuditStatus
  progress: number // 0-100
  current_step?: string
  estimated_time_remaining?: number
  message?: string
}

export interface AuditStatistics {
  total_audits: number
  pending_audits: number
  running_audits: number
  completed_audits: number
  failed_audits: number
  total_vulnerabilities: number
}

export interface VulnerabilitySummary {
  total: number
  critical: number
  high: number
  medium: number
  low: number
}

export interface CreateAuditForm {
  project_id: number
  type: AuditType
  description?: string
}

export interface AuditQueryParams {
  project_id?: number
  status?: AuditStatus
  type?: AuditType
  skip?: number
  limit?: number
}

export interface AuditCancelRequest {
  reason?: string
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'audit_progress' | 'audit_status' | 'audit_log'
  data: any
}

export interface AuditProgressMessage {
  audit_id: number
  progress: AuditProgress
}

export interface AuditStatusMessage {
  audit_id: number
  status: AuditStatus
  message?: string
}

export interface AuditLogMessage {
  audit_id: number
  log: AuditLog
}
