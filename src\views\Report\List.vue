<template>
  <div class="report-list">
    <div class="report-header">
      <h2>报告管理</h2>
      <a-space>
        <a-button @click="handleRefresh" :loading="reportStore.loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="$router.push('/report/generate')">
          <template #icon>
            <PlusOutlined />
          </template>
          生成报告
        </a-button>
      </a-space>
    </div>
    
    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="statistics-row" v-if="statistics">
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card size="small">
          <a-statistic
            title="总报告数"
            :value="statistics.total_reports"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card size="small">
          <a-statistic
            title="Markdown报告"
            :value="statistics.by_format.markdown || 0"
            :value-style="{ color: '#52c41a' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card size="small">
          <a-statistic
            title="PDF报告"
            :value="statistics.by_format.pdf || 0"
            :value-style="{ color: '#fa541c' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card size="small">
          <a-statistic
            title="待通知"
            :value="statistics.pending_notification"
            :value-style="{ color: '#faad14' }"
          />
        </a-card>
      </a-col>
    </a-row>
    
    <a-card>
      <div class="report-toolbar">
        <a-space>
          <a-select
            v-model:value="auditFilter"
            placeholder="选择审计"
            style="width: 200px"
            @change="handleSearch"
            allow-clear
            show-search
          >
            <a-select-option 
              v-for="audit in audits" 
              :key="audit.id" 
              :value="audit.id"
            >
              审计 #{{ audit.id }}
            </a-select-option>
          </a-select>
          
          <a-select
            v-model:value="formatFilter"
            placeholder="报告格式"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部格式</a-select-option>
            <a-select-option value="markdown">Markdown</a-select-option>
            <a-select-option value="pdf">PDF</a-select-option>
          </a-select>
          
          <a-select
            v-model:value="notifiedFilter"
            placeholder="通知状态"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option :value="true">已通知</a-select-option>
            <a-select-option :value="false">未通知</a-select-option>
          </a-select>
        </a-space>
      </div>
      
      <a-table
        :columns="columns"
        :data-source="reportStore.reports"
        :loading="reportStore.loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'id'">
            <a @click="handlePreview(record)">
              #{{ record.id }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'audit_id'">
            <a @click="$router.push(`/audit/detail/${record.audit_id}`)">
              审计 #{{ record.audit_id }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'format'">
            <a-tag :color="getFormatColor(record.format)">
              {{ getFormatText(record.format) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'notified'">
            <a-tag :color="record.notified ? 'green' : 'orange'">
              {{ record.notified ? '已通知' : '未通知' }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'generated_at'">
            {{ formatDate(record.generated_at) }}
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a @click="handlePreview(record)">预览</a>
              <a @click="handleDownload(record.id)">下载</a>
              <a 
                v-if="!record.notified" 
                @click="handleMarkNotified(record.id)"
              >
                标记已通知
              </a>
              <a-popconfirm
                title="确定要删除这个报告吗？"
                @confirm="handleDelete(record.id)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a style="color: #ff4d4f">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 报告预览模态框 -->
    <a-modal
      v-model:open="showPreviewModal"
      :title="`报告预览 - #${previewReport?.id}`"
      width="80%"
      :footer="null"
      :body-style="{ maxHeight: '70vh', overflow: 'auto' }"
    >
      <div v-if="previewReport" class="report-preview">
        <div class="report-meta">
          <a-descriptions :column="3" size="small">
            <a-descriptions-item label="报告ID">#{{ previewReport.id }}</a-descriptions-item>
            <a-descriptions-item label="审计ID">#{{ previewReport.audit_id }}</a-descriptions-item>
            <a-descriptions-item label="格式">
              <a-tag :color="getFormatColor(previewReport.format)">
                {{ getFormatText(previewReport.format) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="生成时间">
              {{ formatDate(previewReport.generated_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="通知状态">
              <a-tag :color="previewReport.notified ? 'green' : 'orange'">
                {{ previewReport.notified ? '已通知' : '未通知' }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>
        
        <a-divider />
        
        <div class="report-content">
          <!-- Markdown 渲染 -->
          <div 
            v-if="previewReport.format === 'markdown'"
            class="markdown-content"
            v-html="renderMarkdown(previewReport.content)"
          ></div>
          
          <!-- PDF 预览提示 -->
          <div v-else-if="previewReport.format === 'pdf'" class="pdf-preview">
            <a-result
              status="info"
              title="PDF报告"
              sub-title="PDF格式报告无法在线预览，请下载后查看"
            >
              <template #extra>
                <a-button type="primary" @click="handleDownload(previewReport.id)">
                  下载PDF报告
                </a-button>
              </template>
            </a-result>
          </div>
          
          <!-- 其他格式 -->
          <div v-else class="raw-content">
            <pre>{{ previewReport.content }}</pre>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useReportStore } from '@/stores/report'
import { useAuditStore } from '@/stores/audit'
import { formatDate } from '@/utils/date'
import { marked } from 'marked'
import { 
  ReloadOutlined, 
  PlusOutlined
} from '@ant-design/icons-vue'
import type { 
  ReportQueryParams, 
  ReportFormat,
  Report
} from '@/types/report'
import type { TableColumnsType } from 'ant-design-vue'

const router = useRouter()
const reportStore = useReportStore()
const auditStore = useAuditStore()

const auditFilter = ref<number>()
const formatFilter = ref<ReportFormat>()
const notifiedFilter = ref<boolean>()

const showPreviewModal = ref(false)
const previewReport = ref<Report | null>(null)

const statistics = computed(() => reportStore.statistics)
const audits = computed(() => auditStore.audits)

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns: TableColumnsType = [
  {
    title: '报告ID',
    dataIndex: 'id',
    key: 'id',
    width: 100,
    sorter: (a: any, b: any) => a.id - b.id
  },
  {
    title: '审计',
    dataIndex: 'audit_id',
    key: 'audit_id',
    width: 120
  },
  {
    title: '格式',
    dataIndex: 'format',
    key: 'format',
    width: 100,
    filters: [
      { text: 'Markdown', value: 'markdown' },
      { text: 'PDF', value: 'pdf' }
    ],
    onFilter: (value: string, record: any) => record.format === value
  },
  {
    title: '通知状态',
    dataIndex: 'notified',
    key: 'notified',
    width: 100,
    filters: [
      { text: '已通知', value: true },
      { text: '未通知', value: false }
    ],
    onFilter: (value: boolean, record: any) => record.notified === value
  },
  {
    title: '生成时间',
    dataIndex: 'generated_at',
    key: 'generated_at',
    width: 180,
    sorter: (a: any, b: any) => new Date(a.generated_at).getTime() - new Date(b.generated_at).getTime()
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

const getFormatColor = (format: ReportFormat) => {
  const colorMap: Record<ReportFormat, string> = {
    markdown: 'blue',
    pdf: 'red'
  }
  return colorMap[format] || 'default'
}

const getFormatText = (format: ReportFormat) => {
  const textMap: Record<ReportFormat, string> = {
    markdown: 'Markdown',
    pdf: 'PDF'
  }
  return textMap[format] || format
}

const renderMarkdown = (content: string) => {
  try {
    return marked(content)
  } catch (error) {
    console.error('Markdown render error:', error)
    return `<pre>${content}</pre>`
  }
}

const handleSearch = () => {
  fetchReports()
}

const handleRefresh = () => {
  fetchReports()
  reportStore.fetchStatistics()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchReports()
}

const handlePreview = (report: Report) => {
  previewReport.value = report
  showPreviewModal.value = true
}

const handleDownload = async (id: number) => {
  try {
    await reportStore.downloadReport(id)
  } catch (error) {
    console.error('Download report error:', error)
  }
}

const handleMarkNotified = async (id: number) => {
  try {
    await reportStore.markAsNotified(id)
    fetchReports()
  } catch (error) {
    console.error('Mark notified error:', error)
  }
}

const handleDelete = async (id: number) => {
  try {
    await reportStore.deleteReport(id)
    fetchReports()
  } catch (error) {
    console.error('Delete report error:', error)
  }
}

const fetchReports = async () => {
  const params: ReportQueryParams = {
    skip: (pagination.current - 1) * pagination.pageSize,
    limit: pagination.pageSize
  }
  
  if (auditFilter.value) {
    params.audit_id = auditFilter.value
  }
  
  if (formatFilter.value) {
    params.format = formatFilter.value
  }
  
  if (notifiedFilter.value !== undefined) {
    params.notified = notifiedFilter.value
  }
  
  try {
    await reportStore.fetchReports(params)
    pagination.total = reportStore.total
  } catch (error) {
    console.error('Fetch reports error:', error)
  }
}

onMounted(async () => {
  // 获取审计列表用于过滤
  await auditStore.fetchAudits()
  
  // 获取报告列表和统计
  await fetchReports()
  await reportStore.fetchStatistics()
})
</script>

<style scoped>
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.report-header h2 {
  margin: 0;
}

.statistics-row {
  margin-bottom: 16px;
}

.report-toolbar {
  margin-bottom: 16px;
}

.report-preview {
  padding: 0;
}

.report-meta {
  background: #fafafa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.report-content {
  max-height: 500px;
  overflow-y: auto;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3) {
  margin-top: 24px;
  margin-bottom: 16px;
  color: #262626;
}

.markdown-content :deep(h1) {
  font-size: 24px;
  border-bottom: 1px solid #d9d9d9;
  padding-bottom: 8px;
}

.markdown-content :deep(h2) {
  font-size: 20px;
}

.markdown-content :deep(h3) {
  font-size: 16px;
}

.markdown-content :deep(p) {
  margin-bottom: 16px;
}

.markdown-content :deep(code) {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.markdown-content :deep(pre) {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 16px 0;
}

.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content :deep(th) {
  background: #fafafa;
  font-weight: 500;
}

.pdf-preview {
  text-align: center;
  padding: 40px 20px;
}

.raw-content pre {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 12px;
  line-height: 1.4;
}

:deep(.ant-table-cell) {
  padding: 12px 8px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}
</style>
