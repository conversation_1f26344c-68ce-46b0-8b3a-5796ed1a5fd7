<template>
  <div class="vulnerability-index">
    <a-card title="漏洞管理">
      <p>这是漏洞管理的入口页面，包含以下功能：</p>
      <a-space direction="vertical" size="middle">
        <router-link to="/vulnerability/list">
          <a-button type="primary" size="large">
            <template #icon>
              <BugOutlined />
            </template>
            漏洞列表
          </a-button>
        </router-link>
      </a-space>
      
      <a-divider />
      
      <div class="feature-list">
        <h3>功能特性</h3>
        <ul>
          <li>📊 漏洞统计图表展示</li>
          <li>🔍 多维度筛选和搜索</li>
          <li>📋 表格展开查看详细信息</li>
          <li>🔄 批量状态更新</li>
          <li>📤 数据导出功能</li>
          <li>🏷️ 严重程度标签显示</li>
          <li>📝 详细的漏洞信息管理</li>
        </ul>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { BugOutlined } from '@ant-design/icons-vue'
</script>

<style scoped>
.vulnerability-index {
  max-width: 600px;
  margin: 0 auto;
}

.feature-list {
  margin-top: 16px;
}

.feature-list h3 {
  margin-bottom: 12px;
  color: #262626;
}

.feature-list ul {
  list-style: none;
  padding: 0;
}

.feature-list li {
  padding: 4px 0;
  font-size: 14px;
  color: #595959;
}
</style>
