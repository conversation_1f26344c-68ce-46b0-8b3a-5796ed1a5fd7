import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getToken } from '@/utils/auth'
import type { WebSocketMessage } from '@/types/notification'

export const useWebSocketStore = defineStore('websocket', () => {
  const socket = ref<WebSocket | null>(null)
  const connected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectInterval = ref<NodeJS.Timeout | null>(null)
  const messageHandlers = ref<Array<(message: WebSocketMessage) => void>>([])

  // 连接WebSocket
  const connect = () => {
    const token = getToken()
    if (!token) {
      console.warn('No token available for WebSocket connection')
      return
    }

    try {
      // 构建WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.host
      const wsUrl = `${protocol}//${host}/ws?token=${token}`
      
      socket.value = new WebSocket(wsUrl)
      
      socket.value.onopen = () => {
        console.log('WebSocket connected')
        connected.value = true
        reconnectAttempts.value = 0
        
        // 清除重连定时器
        if (reconnectInterval.value) {
          clearInterval(reconnectInterval.value)
          reconnectInterval.value = null
        }
      }
      
      socket.value.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          console.log('WebSocket message received:', message)
          
          // 调用所有消息处理器
          messageHandlers.value.forEach(handler => {
            try {
              handler(message)
            } catch (error) {
              console.error('Error in message handler:', error)
            }
          })
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }
      
      socket.value.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        connected.value = false
        
        // 如果不是主动关闭，尝试重连
        if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }
      
      socket.value.onerror = (error) => {
        console.error('WebSocket error:', error)
        connected.value = false
      }
    } catch (error) {
      console.error('Error creating WebSocket connection:', error)
    }
  }

  // 安排重连
  const scheduleReconnect = () => {
    if (reconnectInterval.value) return
    
    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000) // 指数退避，最大30秒
    
    console.log(`Scheduling WebSocket reconnect in ${delay}ms (attempt ${reconnectAttempts.value + 1}/${maxReconnectAttempts})`)
    
    reconnectInterval.value = setTimeout(() => {
      reconnectAttempts.value++
      connect()
    }, delay)
  }

  // 断开连接
  const disconnect = () => {
    if (reconnectInterval.value) {
      clearInterval(reconnectInterval.value)
      reconnectInterval.value = null
    }
    
    if (socket.value) {
      socket.value.close(1000, 'Manual disconnect')
      socket.value = null
    }
    
    connected.value = false
    reconnectAttempts.value = 0
  }

  // 发送消息
  const sendMessage = (message: any) => {
    if (socket.value && connected.value) {
      try {
        socket.value.send(JSON.stringify(message))
      } catch (error) {
        console.error('Error sending WebSocket message:', error)
      }
    } else {
      console.warn('WebSocket not connected, cannot send message')
    }
  }

  // 添加消息处理器
  const onMessage = (handler: (message: WebSocketMessage) => void) => {
    messageHandlers.value.push(handler)
  }

  // 移除消息处理器
  const offMessage = (handler: (message: WebSocketMessage) => void) => {
    const index = messageHandlers.value.indexOf(handler)
    if (index > -1) {
      messageHandlers.value.splice(index, 1)
    }
  }

  // 清除所有消息处理器
  const clearMessageHandlers = () => {
    messageHandlers.value = []
  }

  return {
    socket,
    connected,
    reconnectAttempts,
    connect,
    disconnect,
    sendMessage,
    onMessage,
    offMessage,
    clearMessageHandlers
  }
})
