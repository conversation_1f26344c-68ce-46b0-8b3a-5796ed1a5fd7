<template>
  <div class="profile-container">
    <a-card title="个人信息" class="profile-card">
      <template #extra>
        <a-button 
          type="primary" 
          @click="showEditModal = true"
          :loading="userStore.loading"
        >
          编辑资料
        </a-button>
      </template>
      
      <a-descriptions :column="2" v-if="userStore.user">
        <a-descriptions-item label="用户名">
          {{ userStore.user.username }}
        </a-descriptions-item>
        <a-descriptions-item label="邮箱">
          {{ userStore.user.email }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="userStore.user.is_active ? 'green' : 'red'">
            {{ userStore.user.is_active ? '活跃' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="注册时间">
          {{ formatDate(userStore.user.created_at) }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间">
          {{ formatDate(userStore.user.updated_at) }}
        </a-descriptions-item>
        <a-descriptions-item label="最后登录">
          {{ userStore.user.last_login_at ? formatDate(userStore.user.last_login_at) : '未知' }}
        </a-descriptions-item>
      </a-descriptions>
      
      <a-divider />
      
      <a-button 
        type="default" 
        @click="showPasswordModal = true"
        :icon="h(KeyOutlined)"
      >
        修改密码
      </a-button>
    </a-card>
    
    <!-- 编辑资料模态框 -->
    <a-modal
      v-model:open="showEditModal"
      title="编辑个人资料"
      @ok="handleUpdateProfile"
      @cancel="handleCancelEdit"
      :confirm-loading="userStore.loading"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username">
          <a-input 
            v-model:value="editForm.username" 
            placeholder="请输入用户名"
          />
        </a-form-item>
        
        <a-form-item label="邮箱" name="email">
          <a-input 
            v-model:value="editForm.email" 
            placeholder="请输入邮箱地址"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 修改密码模态框 -->
    <a-modal
      v-model:open="showPasswordModal"
      title="修改密码"
      @ok="handleChangePassword"
      @cancel="handleCancelPassword"
      :confirm-loading="userStore.loading"
    >
      <a-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        layout="vertical"
      >
        <a-form-item label="当前密码" name="current_password">
          <a-input-password 
            v-model:value="passwordForm.current_password" 
            placeholder="请输入当前密码"
          />
        </a-form-item>
        
        <a-form-item label="新密码" name="new_password">
          <a-input-password 
            v-model:value="passwordForm.new_password" 
            placeholder="请输入新密码（至少8位，包含大小写字母和数字）"
          />
        </a-form-item>
        
        <a-form-item label="确认新密码" name="confirm_password">
          <a-input-password 
            v-model:value="passwordForm.confirm_password" 
            placeholder="请再次输入新密码"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils/date'
import { isValidEmail } from '@/utils/common'
import { KeyOutlined } from '@ant-design/icons-vue'
import type { UserUpdate, ChangePasswordForm } from '@/types/user'
import type { FormInstance } from 'ant-design-vue'

const userStore = useUserStore()

const showEditModal = ref(false)
const showPasswordModal = ref(false)
const editFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

const editForm = reactive<UserUpdate>({
  username: '',
  email: ''
})

const passwordForm = reactive<ChangePasswordForm>({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度为3-50个字符', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9_-]+$/, 
      message: '用户名只能包含字母、数字、下划线、连字符', 
      trigger: 'blur' 
    }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { 
      validator: (_: any, value: string) => {
        if (!value) return Promise.resolve()
        if (!isValidEmail(value)) {
          return Promise.reject(new Error('请输入有效的邮箱地址'))
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ]
}

const passwordRules = {
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, max: 128, message: '密码长度为8-128位', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/, 
      message: '密码必须包含大小写字母和数字', 
      trigger: 'blur' 
    }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { 
      validator: (_: any, value: string) => {
        if (!value) return Promise.resolve()
        if (value !== passwordForm.new_password) {
          return Promise.reject(new Error('两次输入的密码不一致'))
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ]
}

const handleUpdateProfile = async () => {
  try {
    await editFormRef.value?.validate()
    await userStore.updateUserInfo(editForm)
    showEditModal.value = false
  } catch (error) {
    console.error('Update profile error:', error)
  }
}

const handleCancelEdit = () => {
  showEditModal.value = false
  editFormRef.value?.resetFields()
  // 重置表单数据
  if (userStore.user) {
    editForm.username = userStore.user.username
    editForm.email = userStore.user.email
  }
}

const handleChangePassword = async () => {
  try {
    await passwordFormRef.value?.validate()
    await userStore.changePassword(passwordForm)
    showPasswordModal.value = false
    passwordFormRef.value?.resetFields()
  } catch (error) {
    console.error('Change password error:', error)
  }
}

const handleCancelPassword = () => {
  showPasswordModal.value = false
  passwordFormRef.value?.resetFields()
}

onMounted(async () => {
  // 获取用户信息
  if (!userStore.user) {
    await userStore.fetchUserInfo()
  }
  
  // 初始化编辑表单
  if (userStore.user) {
    editForm.username = userStore.user.username
    editForm.email = userStore.user.email
  }
})
</script>

<style scoped>
.profile-container {
  max-width: 800px;
  margin: 0 auto;
}

.profile-card {
  margin-bottom: 24px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}
</style>
