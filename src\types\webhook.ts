// 基于API文档的Webhook类型定义
export interface Webhook {
  id: number
  name: string
  description?: string | null
  url: string
  events: string[]
  is_active: boolean
  last_status?: string | null
  last_sent_at?: string | null
  success_count: number
  failure_count: number
  timeout_seconds: number
  retry_count: number
  created_at: string
  updated_at: string
}

export interface WebhookCreateRequest {
  name: string
  description?: string | null
  url: string
  events: string[]
  headers?: Record<string, string> | null
  secret?: string | null
  timeout_seconds?: number
  retry_count?: number
}

export interface WebhookUpdateRequest {
  name?: string | null
  description?: string | null
  url?: string | null
  events?: string[] | null
  headers?: Record<string, string> | null
  secret?: string | null
  timeout_seconds?: number | null
  retry_count?: number | null
  is_active?: boolean | null
}

export interface WebhookEventResponse {
  id: number
  event_type: string
  status: string
  status_code?: number | null
  response_body?: string | null
  error_message?: string | null
  sent_at: string
  response_time_ms: number
  retry_count: number
  created_at: string
}

export interface WebhookEventsListResponse {
  events: Array<Record<string, string>>
}

export interface WebhookQueryParams {
  is_active?: boolean
  skip?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface WebhookTestRequest {
  event_type?: string
  test_data?: Record<string, any>
}

export interface WebhookTestResponse {
  success: boolean
  status_code?: number
  response_body?: string
  error_message?: string
  response_time_ms: number
}

// 事件类型定义
export interface WebhookEventType {
  key: string
  name: string
  description: string
}

// 统计信息
export interface WebhookStats {
  total_webhooks: number
  active_webhooks: number
  inactive_webhooks: number
  total_events: number
  success_rate: number
}
