<template>
  <div class="vulnerability-list">
    <div class="vulnerability-header">
      <h2>漏洞管理</h2>
      <a-space>
        <a-button @click="handleRefresh" :loading="vulnerabilityStore.loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        <a-button @click="handleExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </a-space>
    </div>
    
    <!-- 统计图表 -->
    <a-row :gutter="[16, 16]" class="statistics-row" v-if="statistics">
      <a-col :xs="24" :lg="12">
        <a-card title="严重程度分布" size="small">
          <v-chart class="chart" :option="severityChartOption" autoresize />
        </a-card>
      </a-col>
      <a-col :xs="24" :lg="12">
        <a-card title="状态分布" size="small">
          <v-chart class="chart" :option="statusChartOption" autoresize />
        </a-card>
      </a-col>
    </a-row>
    
    <a-card>
      <div class="vulnerability-toolbar">
        <a-space wrap>
          <a-select
            v-model:value="auditFilter"
            placeholder="选择审计"
            style="width: 200px"
            @change="handleSearch"
            allow-clear
            show-search
          >
            <a-select-option 
              v-for="audit in audits" 
              :key="audit.id" 
              :value="audit.id"
            >
              审计 #{{ audit.id }}
            </a-select-option>
          </a-select>
          
          <a-select
            v-model:value="severityFilter"
            placeholder="严重程度"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高危</a-select-option>
            <a-select-option value="medium">中危</a-select-option>
            <a-select-option value="low">低危</a-select-option>
          </a-select>
          
          <a-select
            v-model:value="statusFilter"
            placeholder="状态"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="confirmed">已确认</a-select-option>
            <a-select-option value="unexploitable">不可利用</a-select-option>
            <a-select-option value="manual_review">待审核</a-select-option>
            <a-select-option value="unknown">未知</a-select-option>
          </a-select>
          
          <a-select
            v-model:value="typeFilter"
            placeholder="类型"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="sensitive_info">敏感信息</a-select-option>
            <a-select-option value="file_audit">文件审计</a-select-option>
            <a-select-option value="entry_point">入口点</a-select-option>
            <a-select-option value="sensitive_func">敏感函数</a-select-option>
          </a-select>
          
          <a-input
            v-model:value="filePathFilter"
            placeholder="文件路径"
            style="width: 200px"
            @pressEnter="handleSearch"
            allow-clear
          />
        </a-space>
      </div>
      
      <!-- 批量操作 -->
      <div class="bulk-actions" v-if="selectedRowKeys.length > 0">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-select
            v-model:value="bulkStatus"
            placeholder="批量更新状态"
            style="width: 150px"
          >
            <a-select-option value="confirmed">已确认</a-select-option>
            <a-select-option value="unexploitable">不可利用</a-select-option>
            <a-select-option value="manual_review">待审核</a-select-option>
            <a-select-option value="unknown">未知</a-select-option>
          </a-select>
          <a-input
            v-model:value="bulkReason"
            placeholder="更新原因"
            style="width: 200px"
          />
          <a-button type="primary" @click="handleBulkUpdate" :loading="vulnerabilityStore.loading">
            批量更新
          </a-button>
          <a-button @click="clearSelection">取消选择</a-button>
        </a-space>
      </div>
      
      <a-table
        :columns="columns"
        :data-source="vulnerabilityStore.vulnerabilities"
        :loading="vulnerabilityStore.loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :expandable="expandable"
        @change="handleTableChange"
        row-key="id"
        :scroll="{ x: 1400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'id'">
            <a @click="$router.push(`/vulnerability/detail/${record.id}`)">
              #{{ record.id }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'file_path'">
            <a-tooltip :title="record.file_path">
              <span class="file-path">{{ getShortPath(record.file_path) }}</span>
            </a-tooltip>
          </template>
          
          <template v-else-if="column.key === 'severity'">
            <a-tag :color="getSeverityColor(record.severity)">
              {{ getSeverityText(record.severity) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'audit_id'">
            <a @click="$router.push(`/audit/detail/${record.audit_id}`)">
              审计 #{{ record.audit_id }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a @click="$router.push(`/vulnerability/detail/${record.id}`)">查看</a>
              <a-dropdown>
                <a>更新状态 <DownOutlined /></a>
                <template #overlay>
                  <a-menu @click="({ key }) => handleStatusUpdate(record.id, key)">
                    <a-menu-item key="confirmed">已确认</a-menu-item>
                    <a-menu-item key="unexploitable">不可利用</a-menu-item>
                    <a-menu-item key="manual_review">待审核</a-menu-item>
                    <a-menu-item key="unknown">未知</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
        
        <!-- 展开行内容 -->
        <template #expandedRowRender="{ record }">
          <div class="expanded-content">
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="完整路径">
                {{ record.file_path }}
              </a-descriptions-item>
              <a-descriptions-item label="行号">
                {{ record.line_number || '未指定' }}
              </a-descriptions-item>
              <a-descriptions-item label="提交ID" v-if="record.commit_id">
                <a-typography-text 
                  :copyable="{ text: record.commit_id }"
                  style="font-family: monospace; font-size: 12px;"
                >
                  {{ record.commit_id.substring(0, 8) }}
                </a-typography-text>
              </a-descriptions-item>
              <a-descriptions-item label="状态原因" v-if="record.reason">
                {{ record.reason }}
              </a-descriptions-item>
              <a-descriptions-item label="详细描述" :span="2">
                {{ record.description }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import { useAuditStore } from '@/stores/audit'
import { formatDate } from '@/utils/date'
import { 
  ReloadOutlined, 
  ExportOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { 
  VulnerabilityFilterParams, 
  VulnerabilitySeverity, 
  VulnerabilityStatus,
  VulnerabilityType
} from '@/types/vulnerability'
import type { TableColumnsType } from 'ant-design-vue'
import { Modal } from 'ant-design-vue'

const router = useRouter()
const vulnerabilityStore = useVulnerabilityStore()
const auditStore = useAuditStore()

const auditFilter = ref<number>()
const severityFilter = ref<VulnerabilitySeverity>()
const statusFilter = ref<VulnerabilityStatus>()
const typeFilter = ref<VulnerabilityType>()
const filePathFilter = ref('')

const selectedRowKeys = ref<number[]>([])
const bulkStatus = ref<VulnerabilityStatus>()
const bulkReason = ref('')

const statistics = computed(() => vulnerabilityStore.statistics)
const audits = computed(() => auditStore.audits)

const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns: TableColumnsType = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80,
    sorter: (a: any, b: any) => a.id - b.id
  },
  {
    title: '文件路径',
    dataIndex: 'file_path',
    key: 'file_path',
    width: 250,
    ellipsis: true
  },
  {
    title: '行号',
    dataIndex: 'line_number',
    key: 'line_number',
    width: 80,
    sorter: (a: any, b: any) => (a.line_number || 0) - (b.line_number || 0)
  },
  {
    title: '严重程度',
    dataIndex: 'severity',
    key: 'severity',
    width: 100,
    filters: [
      { text: '严重', value: 'critical' },
      { text: '高危', value: 'high' },
      { text: '中危', value: 'medium' },
      { text: '低危', value: 'low' }
    ],
    onFilter: (value: string, record: any) => record.severity === value
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    filters: [
      { text: '已确认', value: 'confirmed' },
      { text: '不可利用', value: 'unexploitable' },
      { text: '待审核', value: 'manual_review' },
      { text: '未知', value: 'unknown' }
    ],
    onFilter: (value: string, record: any) => record.status === value
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '审计',
    dataIndex: 'audit_id',
    key: 'audit_id',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
    sorter: (a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

const expandable = {
  expandedRowRender: () => {},
  rowExpandable: () => true
}

// 图表配置
const severityChartOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [{
    type: 'pie',
    radius: '60%',
    data: statistics.value ? [
      { value: statistics.value.by_severity.critical, name: '严重', itemStyle: { color: '#f5222d' } },
      { value: statistics.value.by_severity.high, name: '高危', itemStyle: { color: '#fa541c' } },
      { value: statistics.value.by_severity.medium, name: '中危', itemStyle: { color: '#faad14' } },
      { value: statistics.value.by_severity.low, name: '低危', itemStyle: { color: '#52c41a' } }
    ] : []
  }]
}))

const statusChartOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [{
    type: 'pie',
    radius: '60%',
    data: statistics.value ? [
      { value: statistics.value.by_status.confirmed, name: '已确认', itemStyle: { color: '#1890ff' } },
      { value: statistics.value.by_status.unexploitable, name: '不可利用', itemStyle: { color: '#52c41a' } },
      { value: statistics.value.by_status.manual_review, name: '待审核', itemStyle: { color: '#faad14' } },
      { value: statistics.value.by_status.unknown, name: '未知', itemStyle: { color: '#d9d9d9' } }
    ] : []
  }]
}))

const getSeverityColor = (severity: VulnerabilitySeverity) => {
  const colorMap: Record<VulnerabilitySeverity, string> = {
    critical: 'red',
    high: 'orange',
    medium: 'gold',
    low: 'green'
  }
  return colorMap[severity] || 'default'
}

const getSeverityText = (severity: VulnerabilitySeverity) => {
  const textMap: Record<VulnerabilitySeverity, string> = {
    critical: '严重',
    high: '高危',
    medium: '中危',
    low: '低危'
  }
  return textMap[severity] || severity
}

const getStatusColor = (status: VulnerabilityStatus) => {
  const colorMap: Record<VulnerabilityStatus, string> = {
    confirmed: 'red',
    unexploitable: 'green',
    manual_review: 'orange',
    unknown: 'default'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: VulnerabilityStatus) => {
  const textMap: Record<VulnerabilityStatus, string> = {
    confirmed: '已确认',
    unexploitable: '不可利用',
    manual_review: '待审核',
    unknown: '未知'
  }
  return textMap[status] || status
}

const getTypeColor = (type: VulnerabilityType) => {
  const colorMap: Record<VulnerabilityType, string> = {
    sensitive_info: 'purple',
    file_audit: 'blue',
    entry_point: 'cyan',
    sensitive_func: 'magenta',
    new: 'green',
    fixed: 'lime',
    partial_fixed: 'orange'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: VulnerabilityType) => {
  const textMap: Record<VulnerabilityType, string> = {
    sensitive_info: '敏感信息',
    file_audit: '文件审计',
    entry_point: '入口点',
    sensitive_func: '敏感函数',
    new: '新增',
    fixed: '已修复',
    partial_fixed: '部分修复'
  }
  return textMap[type] || type
}

const getShortPath = (path: string) => {
  if (path.length <= 40) return path
  const parts = path.split('/')
  if (parts.length <= 2) return path
  return `.../${parts.slice(-2).join('/')}`
}

const handleSearch = () => {
  fetchVulnerabilities()
}

const handleRefresh = () => {
  fetchVulnerabilities()
  vulnerabilityStore.fetchStatistics()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchVulnerabilities()
}

const handleStatusUpdate = async (id: number, status: string) => {
  try {
    await vulnerabilityStore.updateVulnerabilityStatus(id, { 
      status: status as VulnerabilityStatus 
    })
    fetchVulnerabilities()
  } catch (error) {
    console.error('Update status error:', error)
  }
}

const handleBulkUpdate = async () => {
  if (!bulkStatus.value) {
    Modal.warning({
      title: '请选择状态',
      content: '请先选择要更新的状态'
    })
    return
  }

  try {
    await vulnerabilityStore.bulkUpdateVulnerabilities({
      vulnerability_ids: selectedRowKeys.value,
      status: bulkStatus.value,
      reason: bulkReason.value || undefined
    })
    clearSelection()
    fetchVulnerabilities()
  } catch (error) {
    console.error('Bulk update error:', error)
  }
}

const clearSelection = () => {
  selectedRowKeys.value = []
  bulkStatus.value = undefined
  bulkReason.value = ''
}

const handleExport = () => {
  Modal.confirm({
    title: '导出漏洞数据',
    content: '确定要导出当前筛选条件下的漏洞数据吗？',
    onOk: async () => {
      const params = buildFilterParams()
      await vulnerabilityStore.exportVulnerabilities(params, 'csv')
    }
  })
}

const buildFilterParams = (): VulnerabilityFilterParams => {
  const params: VulnerabilityFilterParams = {
    skip: (pagination.current - 1) * pagination.pageSize,
    limit: pagination.pageSize
  }
  
  if (auditFilter.value) {
    params.audit_ids = [auditFilter.value]
  }
  
  if (severityFilter.value) {
    params.severities = [severityFilter.value]
  }
  
  if (statusFilter.value) {
    params.statuses = [statusFilter.value]
  }
  
  if (typeFilter.value) {
    params.types = [typeFilter.value]
  }
  
  if (filePathFilter.value) {
    params.file_paths = [filePathFilter.value]
  }
  
  return params
}

const fetchVulnerabilities = async () => {
  const params = buildFilterParams()
  
  try {
    await vulnerabilityStore.fetchVulnerabilitiesFiltered(params)
    pagination.total = vulnerabilityStore.total
  } catch (error) {
    console.error('Fetch vulnerabilities error:', error)
  }
}

onMounted(async () => {
  // 获取审计列表用于过滤
  await auditStore.fetchAudits()
  
  // 获取漏洞列表和统计
  await fetchVulnerabilities()
  await vulnerabilityStore.fetchStatistics()
})
</script>

<style scoped>
.vulnerability-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.vulnerability-header h2 {
  margin: 0;
}

.statistics-row {
  margin-bottom: 16px;
}

.chart {
  height: 200px;
}

.vulnerability-toolbar {
  margin-bottom: 16px;
}

.bulk-actions {
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.file-path {
  font-family: monospace;
  font-size: 12px;
}

.expanded-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

:deep(.ant-table-cell) {
  padding: 12px 8px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}
</style>
