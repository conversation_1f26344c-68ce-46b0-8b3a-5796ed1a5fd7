<template>
  <div class="project-create">
    <a-page-header
      title="创建项目"
      sub-title="创建新的代码审计项目"
      @back="$router.go(-1)"
    />
    
    <a-card>
      <a-form
        ref="formRef"
        :model="projectForm"
        :rules="rules"
        @finish="handleSubmit"
        layout="vertical"
        :style="{ maxWidth: '600px' }"
      >
        <a-form-item label="项目名称" name="name">
          <a-input 
            v-model:value="projectForm.name" 
            placeholder="请输入项目名称（不能包含特殊字符）"
            size="large"
          />
        </a-form-item>
        
        <a-form-item label="项目类型" name="type">
          <a-radio-group v-model:value="projectForm.type" size="large">
            <a-radio-button value="git">Git项目</a-radio-button>
            <a-radio-button value="docker">Docker项目</a-radio-button>
          </a-radio-group>
        </a-form-item>
        
        <!-- Git项目配置 -->
        <template v-if="projectForm.type === 'git'">
          <a-form-item label="Git仓库URL" name="git_url">
            <a-input 
              v-model:value="projectForm.git_url" 
              placeholder="请输入Git仓库URL，如：https://github.com/user/repo.git"
              size="large"
            />
          </a-form-item>
          
          <a-form-item label="Git分支" name="git_branch">
            <a-input 
              v-model:value="projectForm.git_branch" 
              placeholder="请输入Git分支（可选，默认main）"
              size="large"
            />
          </a-form-item>
        </template>
        
        <!-- Docker项目配置 -->
        <template v-if="projectForm.type === 'docker'">
          <a-form-item label="Docker镜像" name="docker_image">
            <a-input 
              v-model:value="projectForm.docker_image" 
              placeholder="请输入Docker镜像，如：nginx:latest"
              size="large"
            />
          </a-form-item>
        </template>
        
        <a-form-item label="源码路径" name="source_path">
          <a-input 
            v-model:value="projectForm.source_path" 
            placeholder="请输入源码路径（可选）"
            size="large"
          />
          <template #extra>
            <span class="text-gray">指定项目中需要审计的源码路径，留空则审计整个项目</span>
          </template>
        </a-form-item>
        
        <a-form-item label="检查间隔" name="check_interval">
          <a-input-number 
            v-model:value="projectForm.check_interval" 
            :min="1" 
            :max="10080"
            placeholder="检查间隔（分钟）"
            size="large"
            style="width: 100%"
          />
          <template #extra>
            <span class="text-gray">自动检查项目更新的间隔时间，默认1440分钟（24小时）</span>
          </template>
        </a-form-item>
        
        <a-form-item label="项目描述" name="description">
          <a-textarea
            v-model:value="projectForm.description"
            placeholder="请输入项目描述（可选）"
            :rows="4"
            size="large"
          />
        </a-form-item>
        
        <a-form-item label="扩展数据" name="extra_data">
          <a-textarea
            v-model:value="extraDataText"
            placeholder="请输入JSON格式的扩展数据（可选）"
            :rows="4"
            size="large"
          />
          <template #extra>
            <span class="text-gray">JSON格式的扩展配置数据，如：{"key": "value"}</span>
          </template>
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button 
              type="primary" 
              html-type="submit" 
              size="large"
              :loading="projectStore.loading"
            >
              创建项目
            </a-button>
            <a-button size="large" @click="$router.go(-1)">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import type { CreateProjectForm } from '@/types/project'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

const router = useRouter()
const projectStore = useProjectStore()
const formRef = ref<FormInstance>()
const extraDataText = ref('')

const projectForm = reactive<CreateProjectForm>({
  name: '',
  type: 'git',
  git_url: '',
  git_branch: '',
  docker_image: '',
  source_path: '',
  check_interval: 1440,
  description: '',
  extra_data: {}
})

const rules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 1, max: 255, message: '项目名称长度为1-255个字符', trigger: 'blur' },
    { 
      pattern: /^[^<>:"/\\|?*]+$/, 
      message: '项目名称不能包含特殊字符', 
      trigger: 'blur' 
    }
  ],
  type: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  git_url: [
    { 
      validator: (_: any, value: string) => {
        if (projectForm.type === 'git' && !value) {
          return Promise.reject(new Error('Git项目必须填写仓库URL'))
        }
        if (value && value.length > 512) {
          return Promise.reject(new Error('Git URL长度不能超过512个字符'))
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ],
  docker_image: [
    { 
      validator: (_: any, value: string) => {
        if (projectForm.type === 'docker' && !value) {
          return Promise.reject(new Error('Docker项目必须填写镜像名称'))
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ],
  check_interval: [
    { required: true, message: '请输入检查间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 10080, message: '检查间隔范围为1-10080分钟', trigger: 'blur' }
  ],
  extra_data: [
    { 
      validator: (_: any, value: any) => {
        if (extraDataText.value) {
          try {
            JSON.parse(extraDataText.value)
          } catch (error) {
            return Promise.reject(new Error('扩展数据必须是有效的JSON格式'))
          }
        }
        return Promise.resolve()
      }, 
      trigger: 'blur' 
    }
  ]
}

// 监听项目类型变化，清空相关字段
watch(() => projectForm.type, (newType) => {
  if (newType === 'git') {
    projectForm.docker_image = ''
  } else if (newType === 'docker') {
    projectForm.git_url = ''
    projectForm.git_branch = ''
  }
})

const handleSubmit = async () => {
  try {
    // 处理扩展数据
    if (extraDataText.value) {
      try {
        projectForm.extra_data = JSON.parse(extraDataText.value)
      } catch (error) {
        message.error('扩展数据格式错误，请输入有效的JSON')
        return
      }
    } else {
      projectForm.extra_data = {}
    }
    
    await projectStore.createProject(projectForm)
    router.push('/project/list')
  } catch (error) {
    console.error('Create project error:', error)
  }
}
</script>

<style scoped>
.project-create {
  max-width: 800px;
  margin: 0 auto;
}

.text-gray {
  color: #666;
  font-size: 12px;
}

:deep(.ant-form-item-extra) {
  margin-top: 4px;
}
</style>
