<template>
  <div class="webhook-detail">
    <a-page-header
      :title="`Webhook - ${webhook?.name}`"
      :sub-title="webhook?.description"
      @back="$router.go(-1)"
    >
      <template #extra>
        <a-space>
          <a-switch
            v-if="webhook"
            :checked="webhook.is_active"
            @change="handleToggleStatus"
            :loading="toggleLoading"
          />
          <span>{{ webhook?.is_active ? '启用' : '禁用' }}</span>
          <a-button @click="handleTest" :loading="testLoading">
            <template #icon>
              <PlayCircleOutlined />
            </template>
            测试
          </a-button>
          <a-button @click="handleEdit">
            <template #icon>
              <EditOutlined />
            </template>
            编辑
          </a-button>
          <a-button @click="handleRefresh" :loading="webhookStore.loading">
            <template #icon>
              <SyncOutlined />
            </template>
            刷新
          </a-button>
          <a-popconfirm
            title="确定要删除这个Webhook吗？"
            @confirm="handleDelete"
            ok-text="确定"
            cancel-text="取消"
          >
            <a-button danger>
              <template #icon>
                <DeleteOutlined />
              </template>
              删除
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </a-page-header>
    
    <a-row :gutter="[16, 16]">
      <!-- Webhook基本信息 -->
      <a-col :span="24">
        <a-card title="基本信息" :loading="webhookStore.loading">
          <a-descriptions :column="3" v-if="webhook">
            <a-descriptions-item label="Webhook ID">#{{ webhook.id }}</a-descriptions-item>
            <a-descriptions-item label="名称">{{ webhook.name }}</a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag :color="webhook.is_active ? 'green' : 'red'">
                {{ webhook.is_active ? '启用' : '禁用' }}
              </a-tag>
            </a-descriptions-item>
            
            <a-descriptions-item label="URL" :span="3">
              <a-typography-text 
                :copyable="{ text: webhook.url }"
                style="font-family: monospace; font-size: 12px;"
              >
                {{ webhook.url }}
              </a-typography-text>
            </a-descriptions-item>
            
            <a-descriptions-item label="超时时间">{{ webhook.timeout_seconds }}秒</a-descriptions-item>
            <a-descriptions-item label="重试次数">{{ webhook.retry_count }}次</a-descriptions-item>
            <a-descriptions-item label="最后状态">
              <a-tag 
                v-if="webhook.last_status"
                :color="getStatusColor(webhook.last_status)"
              >
                {{ webhook.last_status }}
              </a-tag>
              <span v-else class="text-gray">-</span>
            </a-descriptions-item>
            
            <a-descriptions-item label="成功次数">
              <a-tag color="green">{{ webhook.success_count }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="失败次数">
              <a-tag color="red">{{ webhook.failure_count }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="成功率">
              {{ getSuccessRate(webhook.success_count, webhook.failure_count) }}%
            </a-descriptions-item>
            
            <a-descriptions-item label="最后发送时间">
              {{ webhook.last_sent_at ? formatDate(webhook.last_sent_at) : '从未发送' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(webhook.created_at) }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ formatDate(webhook.updated_at) }}
            </a-descriptions-item>
            
            <a-descriptions-item label="描述" :span="3" v-if="webhook.description">
              {{ webhook.description }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>
      
      <!-- 监听事件 -->
      <a-col :span="24">
        <a-card title="监听事件">
          <a-space wrap v-if="webhook?.events">
            <a-tag 
              v-for="event in webhook.events" 
              :key="event"
              color="blue"
              style="margin-bottom: 8px"
            >
              {{ event }}
            </a-tag>
          </a-space>
          <a-empty v-else description="暂无监听事件" />
        </a-card>
      </a-col>
      
      <!-- 事件历史 -->
      <a-col :span="24">
        <a-card title="事件历史">
          <template #extra>
            <a-button @click="fetchEvents" :loading="eventsLoading">
              <template #icon>
                <ReloadOutlined />
              </template>
              刷新
            </a-button>
          </template>
          
          <a-table
            :columns="eventColumns"
            :data-source="webhookStore.webhookEvents"
            :loading="eventsLoading"
            :pagination="eventPagination"
            @change="handleEventTableChange"
            row-key="id"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'event_type'">
                <a-tag color="blue" size="small">{{ record.event_type }}</a-tag>
              </template>
              
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)" size="small">
                  {{ record.status }}
                </a-tag>
              </template>
              
              <template v-else-if="column.key === 'status_code'">
                <a-tag 
                  :color="getStatusCodeColor(record.status_code)" 
                  size="small"
                  v-if="record.status_code"
                >
                  {{ record.status_code }}
                </a-tag>
                <span v-else class="text-gray">-</span>
              </template>
              
              <template v-else-if="column.key === 'response_time_ms'">
                {{ record.response_time_ms }}ms
              </template>
              
              <template v-else-if="column.key === 'sent_at'">
                {{ formatDate(record.sent_at) }}
              </template>
              
              <template v-else-if="column.key === 'actions'">
                <a @click="showEventDetail(record)">详情</a>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 编辑模态框 -->
    <a-modal
      v-model:open="showEditModal"
      title="编辑Webhook"
      @ok="handleUpdate"
      @cancel="handleCancelEdit"
      :confirm-loading="webhookStore.loading"
      width="800px"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        layout="vertical"
      >
        <a-form-item label="Webhook名称" name="name">
          <a-input v-model:value="editForm.name" placeholder="请输入Webhook名称" />
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="editForm.description"
            placeholder="请输入Webhook描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="Webhook URL" name="url">
          <a-input v-model:value="editForm.url" placeholder="请输入Webhook URL" />
        </a-form-item>
        
        <a-form-item label="监听事件" name="events">
          <a-select
            v-model:value="editForm.events"
            mode="multiple"
            placeholder="请选择要监听的事件类型"
            :options="eventOptions"
          />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="超时时间（秒）" name="timeout_seconds">
              <a-input-number 
                v-model:value="editForm.timeout_seconds" 
                :min="1" 
                :max="300"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="重试次数" name="retry_count">
              <a-input-number 
                v-model:value="editForm.retry_count" 
                :min="0" 
                :max="10"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    
    <!-- 事件详情模态框 -->
    <a-modal
      v-model:open="showEventDetailModal"
      :title="`事件详情 - ${selectedEvent?.event_type}`"
      :footer="null"
      width="800px"
    >
      <a-descriptions :column="2" v-if="selectedEvent">
        <a-descriptions-item label="事件类型">
          <a-tag color="blue">{{ selectedEvent.event_type }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(selectedEvent.status)">
            {{ selectedEvent.status }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态码">
          <a-tag 
            :color="getStatusCodeColor(selectedEvent.status_code)" 
            v-if="selectedEvent.status_code"
          >
            {{ selectedEvent.status_code }}
          </a-tag>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="响应时间">
          {{ selectedEvent.response_time_ms }}ms
        </a-descriptions-item>
        <a-descriptions-item label="重试次数">
          {{ selectedEvent.retry_count }}
        </a-descriptions-item>
        <a-descriptions-item label="发送时间">
          {{ formatDate(selectedEvent.sent_at) }}
        </a-descriptions-item>
        <a-descriptions-item label="错误信息" :span="2" v-if="selectedEvent.error_message">
          <pre class="error-message">{{ selectedEvent.error_message }}</pre>
        </a-descriptions-item>
        <a-descriptions-item label="响应内容" :span="2" v-if="selectedEvent.response_body">
          <pre class="response-body">{{ selectedEvent.response_body }}</pre>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useWebhookStore } from '@/stores/webhook'
import { formatDate } from '@/utils/date'
import { 
  PlayCircleOutlined,
  EditOutlined, 
  SyncOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import type { 
  WebhookUpdateRequest, 
  WebhookEventResponse 
} from '@/types/webhook'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'

const route = useRoute()
const router = useRouter()
const webhookStore = useWebhookStore()

const webhook = computed(() => webhookStore.currentWebhook)
const eventOptions = computed(() => 
  webhookStore.availableEvents.map(event => ({
    label: event.name,
    value: event.key
  }))
)

const toggleLoading = ref(false)
const testLoading = ref(false)
const eventsLoading = ref(false)
const showEditModal = ref(false)
const showEventDetailModal = ref(false)
const selectedEvent = ref<WebhookEventResponse | null>(null)
const editFormRef = ref<FormInstance>()

const editForm = reactive<WebhookUpdateRequest>({
  name: '',
  description: '',
  url: '',
  events: [],
  timeout_seconds: 30,
  retry_count: 3
})

const eventPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const eventColumns: TableColumnsType = [
  {
    title: '事件类型',
    dataIndex: 'event_type',
    key: 'event_type',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '状态码',
    dataIndex: 'status_code',
    key: 'status_code',
    width: 100
  },
  {
    title: '响应时间',
    dataIndex: 'response_time_ms',
    key: 'response_time_ms',
    width: 100
  },
  {
    title: '重试次数',
    dataIndex: 'retry_count',
    key: 'retry_count',
    width: 100
  },
  {
    title: '发送时间',
    dataIndex: 'sent_at',
    key: 'sent_at',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 80
  }
]

const editRules = {
  name: [
    { required: true, message: '请输入Webhook名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入Webhook URL', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的HTTP或HTTPS URL', trigger: 'blur' }
  ],
  events: [
    { required: true, message: '请选择至少一个事件类型', trigger: 'change' }
  ]
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'success': 'green',
    'failed': 'red',
    'timeout': 'orange',
    'error': 'red'
  }
  return colorMap[status.toLowerCase()] || 'default'
}

const getStatusCodeColor = (statusCode?: number) => {
  if (!statusCode) return 'default'
  if (statusCode >= 200 && statusCode < 300) return 'green'
  if (statusCode >= 400 && statusCode < 500) return 'orange'
  if (statusCode >= 500) return 'red'
  return 'default'
}

const getSuccessRate = (success: number, failure: number) => {
  const total = success + failure
  if (total === 0) return 0
  return Math.round((success / total) * 100)
}

const handleToggleStatus = async (is_active: boolean) => {
  if (!webhook.value) return
  
  toggleLoading.value = true
  try {
    await webhookStore.toggleWebhook(webhook.value.id, is_active)
  } catch (error) {
    console.error('Toggle webhook status error:', error)
  } finally {
    toggleLoading.value = false
  }
}

const handleTest = async () => {
  if (!webhook.value) return
  
  testLoading.value = true
  try {
    await webhookStore.testWebhook(webhook.value.id)
  } catch (error) {
    console.error('Test webhook error:', error)
  } finally {
    testLoading.value = false
  }
}

const handleEdit = () => {
  if (!webhook.value) return
  
  Object.assign(editForm, {
    name: webhook.value.name,
    description: webhook.value.description,
    url: webhook.value.url,
    events: [...webhook.value.events],
    timeout_seconds: webhook.value.timeout_seconds,
    retry_count: webhook.value.retry_count
  })
  showEditModal.value = true
}

const handleUpdate = async () => {
  try {
    await editFormRef.value?.validate()
    if (webhook.value) {
      await webhookStore.updateWebhook(webhook.value.id, editForm)
      showEditModal.value = false
    }
  } catch (error) {
    console.error('Update webhook error:', error)
  }
}

const handleCancelEdit = () => {
  showEditModal.value = false
  editFormRef.value?.resetFields()
}

const handleDelete = async () => {
  if (!webhook.value) return
  
  try {
    await webhookStore.deleteWebhook(webhook.value.id)
    router.push('/webhook/list')
  } catch (error) {
    console.error('Delete webhook error:', error)
  }
}

const handleRefresh = async () => {
  const webhookId = Number(route.params.id)
  if (webhookId) {
    await webhookStore.fetchWebhook(webhookId)
  }
}

const fetchEvents = async () => {
  const webhookId = Number(route.params.id)
  if (!webhookId) return
  
  eventsLoading.value = true
  try {
    await webhookStore.fetchWebhookEvents(webhookId, {
      skip: (eventPagination.current - 1) * eventPagination.pageSize,
      limit: eventPagination.pageSize
    })
  } catch (error) {
    console.error('Fetch webhook events error:', error)
  } finally {
    eventsLoading.value = false
  }
}

const handleEventTableChange = (pag: any) => {
  eventPagination.current = pag.current
  eventPagination.pageSize = pag.pageSize
  fetchEvents()
}

const showEventDetail = (event: WebhookEventResponse) => {
  selectedEvent.value = event
  showEventDetailModal.value = true
}

onMounted(async () => {
  const webhookId = Number(route.params.id)
  if (webhookId) {
    await webhookStore.fetchWebhook(webhookId)
    await webhookStore.fetchAvailableEvents()
    await fetchEvents()
  }
})
</script>

<style scoped>
.webhook-detail {
  padding: 0;
}

.text-gray {
  color: #999;
}

.error-message,
.response-body {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}
</style>
