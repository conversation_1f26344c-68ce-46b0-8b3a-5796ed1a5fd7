import api from './index'
import type { 
  Report,
  ReportCreate,
  ReportUpdate,
  ReportGenerateRequest,
  ReportQueryParams,
  ReportStats
} from '@/types/report'

export const reportApi = {
  // 获取报告列表
  getReports: (params?: ReportQueryParams) => 
    api.get<Report[]>('/api/v1/reports/', { params }),

  // 获取报告详情
  getReport: (id: number) => 
    api.get<Report>(`/api/v1/reports/${id}`),

  // 创建报告
  createReport: (data: ReportCreate) => 
    api.post<Report>('/api/v1/reports/', data),

  // 生成报告
  generateReport: (auditId: number, data: Omit<ReportGenerateRequest, 'audit_id'>) => 
    api.post<Report>(`/api/v1/reports/${auditId}/generate`, data),

  // 更新报告
  updateReport: (id: number, data: ReportUpdate) => 
    api.put<Report>(`/api/v1/reports/${id}`, data),

  // 删除报告
  deleteReport: (id: number) => 
    api.delete(`/api/v1/reports/${id}`),

  // 下载报告
  downloadReport: (id: number) => 
    api.get(`/api/v1/reports/${id}/download`, {
      responseType: 'blob'
    }),

  // 获取报告统计
  getReportStats: () => 
    api.get<ReportStats>('/api/v1/reports/stats'),

  // 标记报告为已通知
  markAsNotified: (id: number) => 
    api.post<Report>(`/api/v1/reports/${id}/notify`),

  // 预览报告（如果API支持）
  previewReport: (auditId: number, format: string) => 
    api.get<{ content: string }>(`/api/v1/reports/${auditId}/preview`, {
      params: { format }
    }),
}
