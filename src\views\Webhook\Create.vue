<template>
  <div class="webhook-create">
    <a-page-header
      title="创建Webhook"
      sub-title="创建新的Webhook来接收系统事件通知"
      @back="$router.go(-1)"
    />
    
    <a-card>
      <a-form
        ref="formRef"
        :model="webhookForm"
        :rules="rules"
        @finish="handleSubmit"
        layout="vertical"
        :style="{ maxWidth: '800px' }"
      >
        <a-form-item label="Webhook名称" name="name">
          <a-input 
            v-model:value="webhookForm.name" 
            placeholder="请输入Webhook名称"
            size="large"
          />
        </a-form-item>
        
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="webhookForm.description"
            placeholder="请输入Webhook描述（可选）"
            :rows="3"
            size="large"
          />
        </a-form-item>
        
        <a-form-item label="Webhook URL" name="url">
          <a-input 
            v-model:value="webhookForm.url" 
            placeholder="请输入接收Webhook的URL，如：https://example.com/webhook"
            size="large"
          />
          <template #extra>
            <span class="text-gray">系统将向此URL发送POST请求</span>
          </template>
        </a-form-item>
        
        <a-form-item label="监听事件" name="events">
          <a-select
            v-model:value="webhookForm.events"
            mode="multiple"
            placeholder="请选择要监听的事件类型"
            size="large"
            :options="eventOptions"
            :loading="eventsLoading"
            show-search
            :filter-option="filterEventOption"
          />
          <template #extra>
            <span class="text-gray">选择此Webhook要监听的事件类型，可多选</span>
          </template>
        </a-form-item>
        
        <a-form-item label="请求头" name="headers">
          <div class="headers-container">
            <div 
              v-for="(header, index) in headersList" 
              :key="index"
              class="header-item"
            >
              <a-input
                v-model:value="header.key"
                placeholder="Header名称"
                style="width: 200px"
              />
              <a-input
                v-model:value="header.value"
                placeholder="Header值"
                style="width: 300px; margin-left: 8px"
              />
              <a-button 
                @click="removeHeader(index)"
                type="text"
                danger
                style="margin-left: 8px"
              >
                删除
              </a-button>
            </div>
            <a-button @click="addHeader" type="dashed" block>
              <template #icon>
                <PlusOutlined />
              </template>
              添加请求头
            </a-button>
          </div>
          <template #extra>
            <span class="text-gray">自定义HTTP请求头，如Authorization等</span>
          </template>
        </a-form-item>
        
        <a-form-item label="密钥" name="secret">
          <a-input-password
            v-model:value="webhookForm.secret"
            placeholder="请输入用于签名验证的密钥（可选）"
            size="large"
          />
          <template #extra>
            <span class="text-gray">用于生成HMAC签名，确保请求来源的安全性</span>
          </template>
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="超时时间（秒）" name="timeout_seconds">
              <a-input-number 
                v-model:value="webhookForm.timeout_seconds" 
                :min="1" 
                :max="300"
                placeholder="超时时间"
                size="large"
                style="width: 100%"
              />
              <template #extra>
                <span class="text-gray">请求超时时间，默认30秒</span>
              </template>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="重试次数" name="retry_count">
              <a-input-number 
                v-model:value="webhookForm.retry_count" 
                :min="0" 
                :max="10"
                placeholder="重试次数"
                size="large"
                style="width: 100%"
              />
              <template #extra>
                <span class="text-gray">失败时的重试次数，默认3次</span>
              </template>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item>
          <a-space>
            <a-button 
              type="primary" 
              html-type="submit" 
              size="large"
              :loading="webhookStore.loading"
            >
              创建Webhook
            </a-button>
            <a-button size="large" @click="$router.go(-1)">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useWebhookStore } from '@/stores/webhook'
import { PlusOutlined } from '@ant-design/icons-vue'
import type { WebhookCreateRequest } from '@/types/webhook'
import type { FormInstance } from 'ant-design-vue'

const router = useRouter()
const webhookStore = useWebhookStore()
const formRef = ref<FormInstance>()
const eventsLoading = ref(false)

const webhookForm = reactive<WebhookCreateRequest>({
  name: '',
  description: '',
  url: '',
  events: [],
  headers: {},
  secret: '',
  timeout_seconds: 30,
  retry_count: 3
})

const headersList = ref<Array<{ key: string; value: string }>>([])

const eventOptions = computed(() => 
  webhookStore.availableEvents.map(event => ({
    label: event.name,
    value: event.key,
    title: event.description
  }))
)

const rules = {
  name: [
    { required: true, message: '请输入Webhook名称', trigger: 'blur' },
    { min: 1, max: 255, message: 'Webhook名称长度为1-255个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入Webhook URL', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/, 
      message: '请输入有效的HTTP或HTTPS URL', 
      trigger: 'blur' 
    }
  ],
  events: [
    { required: true, message: '请选择至少一个事件类型', trigger: 'change' },
    { type: 'array', min: 1, message: '请选择至少一个事件类型', trigger: 'change' }
  ],
  timeout_seconds: [
    { required: true, message: '请输入超时时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '超时时间范围为1-300秒', trigger: 'blur' }
  ],
  retry_count: [
    { required: true, message: '请输入重试次数', trigger: 'blur' },
    { type: 'number', min: 0, max: 10, message: '重试次数范围为0-10次', trigger: 'blur' }
  ]
}

const filterEventOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0 ||
         option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const addHeader = () => {
  headersList.value.push({ key: '', value: '' })
}

const removeHeader = (index: number) => {
  headersList.value.splice(index, 1)
}

const buildHeaders = () => {
  const headers: Record<string, string> = {}
  headersList.value.forEach(header => {
    if (header.key && header.value) {
      headers[header.key] = header.value
    }
  })
  return Object.keys(headers).length > 0 ? headers : null
}

const handleSubmit = async () => {
  try {
    // 构建请求头
    webhookForm.headers = buildHeaders()
    
    // 清理空值
    if (!webhookForm.description) {
      webhookForm.description = null
    }
    if (!webhookForm.secret) {
      webhookForm.secret = null
    }
    
    await webhookStore.createWebhook(webhookForm)
    router.push('/webhook/list')
  } catch (error) {
    console.error('Create webhook error:', error)
  }
}

onMounted(async () => {
  // 获取可用事件类型
  eventsLoading.value = true
  try {
    await webhookStore.fetchAvailableEvents()
  } catch (error) {
    console.error('Fetch events error:', error)
  } finally {
    eventsLoading.value = false
  }
})
</script>

<style scoped>
.webhook-create {
  max-width: 1000px;
  margin: 0 auto;
}

.text-gray {
  color: #666;
  font-size: 12px;
}

.headers-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.header-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.header-item:last-of-type {
  margin-bottom: 16px;
}

:deep(.ant-form-item-extra) {
  margin-top: 4px;
}

:deep(.ant-select-selector) {
  min-height: 40px;
}

:deep(.ant-select-selection-item) {
  background: #f0f0f0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 8px;
  margin: 2px;
}
</style>
