<template>
  <div class="notification-container">
    <!-- 通知铃铛图标 -->
    <a-badge :count="unreadCount" :overflow-count="99">
      <a-button 
        type="text" 
        shape="circle" 
        size="large"
        @click="showNotifications = !showNotifications"
        :class="{ 'notification-active': showNotifications }"
      >
        <template #icon>
          <BellOutlined />
        </template>
      </a-button>
    </a-badge>
    
    <!-- 通知面板 -->
    <div 
      v-if="showNotifications" 
      class="notification-panel"
      v-click-outside="handleClickOutside"
    >
      <div class="notification-header">
        <span class="notification-title">通知中心</span>
        <a-space>
          <a-button 
            type="text" 
            size="small" 
            @click="markAllAsRead"
            :disabled="unreadCount === 0"
          >
            全部已读
          </a-button>
          <a-button 
            type="text" 
            size="small" 
            @click="clearAll"
          >
            清空
          </a-button>
        </a-space>
      </div>
      
      <div class="notification-tabs">
        <a-tabs v-model:activeKey="activeTab" size="small">
          <a-tab-pane key="all" tab="全部">
            <div class="notification-list">
              <div 
                v-for="notification in filteredNotifications" 
                :key="notification.id"
                class="notification-item"
                :class="{ 'unread': !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-icon">
                  <component 
                    :is="getNotificationIcon(notification.type)" 
                    :style="{ color: getNotificationColor(notification.type) }"
                  />
                </div>
                <div class="notification-content">
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
                </div>
                <div class="notification-actions">
                  <a-button 
                    type="text" 
                    size="small" 
                    @click.stop="removeNotification(notification.id)"
                  >
                    <template #icon>
                      <CloseOutlined />
                    </template>
                  </a-button>
                </div>
              </div>
              
              <div v-if="filteredNotifications.length === 0" class="notification-empty">
                <a-empty description="暂无通知" />
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="unread" :tab="`未读 (${unreadCount})`">
            <div class="notification-list">
              <div 
                v-for="notification in unreadNotifications" 
                :key="notification.id"
                class="notification-item unread"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-icon">
                  <component 
                    :is="getNotificationIcon(notification.type)" 
                    :style="{ color: getNotificationColor(notification.type) }"
                  />
                </div>
                <div class="notification-content">
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
                </div>
                <div class="notification-actions">
                  <a-button 
                    type="text" 
                    size="small" 
                    @click.stop="removeNotification(notification.id)"
                  >
                    <template #icon>
                      <CloseOutlined />
                    </template>
                  </a-button>
                </div>
              </div>
              
              <div v-if="unreadNotifications.length === 0" class="notification-empty">
                <a-empty description="暂无未读通知" />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
      
      <div class="notification-footer">
        <a-button type="link" size="small" @click="$router.push('/notifications')">
          查看全部通知
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '@/stores/notification'
import { useWebSocketStore } from '@/stores/websocket'
import { notification } from 'ant-design-vue'
import { formatDate } from '@/utils/date'
import {
  BellOutlined,
  CloseOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  BugOutlined,
  ProjectOutlined,
  FileTextOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'
import type { NotificationItem } from '@/types/notification'

const router = useRouter()
const notificationStore = useNotificationStore()
const webSocketStore = useWebSocketStore()

const showNotifications = ref(false)
const activeTab = ref('all')

// 计算属性
const notifications = computed(() => notificationStore.notifications)
const unreadCount = computed(() => notificationStore.unreadCount)
const unreadNotifications = computed(() => notifications.value.filter(n => !n.read))
const filteredNotifications = computed(() => {
  if (activeTab.value === 'unread') {
    return unreadNotifications.value
  }
  return notifications.value
})

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    info: InfoCircleOutlined,
    success: CheckCircleOutlined,
    warning: ExclamationCircleOutlined,
    error: CloseCircleOutlined,
    audit: BugOutlined,
    project: ProjectOutlined,
    report: FileTextOutlined,
    webhook: ApiOutlined
  }
  return iconMap[type] || InfoCircleOutlined
}

// 获取通知颜色
const getNotificationColor = (type: string) => {
  const colorMap: Record<string, string> = {
    info: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#f5222d',
    audit: '#722ed1',
    project: '#13c2c2',
    report: '#fa8c16',
    webhook: '#eb2f96'
  }
  return colorMap[type] || '#1890ff'
}

// 格式化时间
const formatTime = (time: string) => {
  const now = new Date()
  const notificationTime = new Date(time)
  const diff = now.getTime() - notificationTime.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return formatDate(time, 'MM-DD HH:mm')
}

// 处理通知点击
const handleNotificationClick = (notificationItem: NotificationItem) => {
  // 标记为已读
  if (!notificationItem.read) {
    notificationStore.markAsRead(notificationItem.id)
  }
  
  // 根据通知类型跳转到相应页面
  if (notificationItem.actionUrl) {
    router.push(notificationItem.actionUrl)
    showNotifications.value = false
  }
}

// 移除通知
const removeNotification = (id: string) => {
  notificationStore.removeNotification(id)
}

// 标记全部已读
const markAllAsRead = () => {
  notificationStore.markAllAsRead()
}

// 清空所有通知
const clearAll = () => {
  notificationStore.clearAll()
}

// 点击外部关闭
const handleClickOutside = () => {
  showNotifications.value = false
}

// WebSocket消息处理
const handleWebSocketMessage = (message: any) => {
  const { type, data } = message
  
  // 根据消息类型创建通知
  let notificationData: Partial<NotificationItem> = {
    type: 'info',
    message: '收到新消息',
    createdAt: new Date().toISOString()
  }
  
  switch (type) {
    case 'audit.completed':
      notificationData = {
        type: 'audit',
        message: `审计 #${data.audit_id} 已完成，发现 ${data.vulnerability_count} 个漏洞`,
        actionUrl: `/audit/detail/${data.audit_id}`,
        createdAt: new Date().toISOString()
      }
      break
      
    case 'audit.failed':
      notificationData = {
        type: 'error',
        message: `审计 #${data.audit_id} 执行失败`,
        actionUrl: `/audit/detail/${data.audit_id}`,
        createdAt: new Date().toISOString()
      }
      break
      
    case 'vulnerability.found':
      notificationData = {
        type: 'warning',
        message: `发现新漏洞：${data.description}`,
        actionUrl: `/vulnerability/detail/${data.vulnerability_id}`,
        createdAt: new Date().toISOString()
      }
      break
      
    case 'report.generated':
      notificationData = {
        type: 'report',
        message: `报告 #${data.report_id} 生成完成`,
        actionUrl: `/report/list`,
        createdAt: new Date().toISOString()
      }
      break
      
    case 'webhook.failed':
      notificationData = {
        type: 'error',
        message: `Webhook "${data.webhook_name}" 发送失败`,
        actionUrl: `/webhook/detail/${data.webhook_id}`,
        createdAt: new Date().toISOString()
      }
      break
  }
  
  // 添加到通知列表
  notificationStore.addNotification(notificationData as NotificationItem)
  
  // 显示系统通知
  notification.open({
    message: notificationData.message,
    description: '点击查看详情',
    icon: h(getNotificationIcon(notificationData.type!), { 
      style: { color: getNotificationColor(notificationData.type!) } 
    }),
    onClick: () => {
      if (notificationData.actionUrl) {
        router.push(notificationData.actionUrl)
      }
    }
  })
}

onMounted(() => {
  // 加载通知数据
  notificationStore.fetchNotifications()
  
  // 监听WebSocket消息
  webSocketStore.onMessage(handleWebSocketMessage)
})

onUnmounted(() => {
  // 清理WebSocket监听
  webSocketStore.offMessage(handleWebSocketMessage)
})
</script>

<style scoped>
.notification-container {
  position: relative;
}

.notification-active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.notification-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 380px;
  max-height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 8px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

.notification-title {
  font-weight: 500;
  font-size: 16px;
}

.notification-tabs {
  padding: 0 16px;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: #f5f5f5;
}

.notification-item.unread {
  background-color: #f6ffed;
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #1890ff;
  border-radius: 2px;
}

.notification-icon {
  margin-right: 12px;
  font-size: 16px;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-message {
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
  word-break: break-word;
}

.notification-time {
  font-size: 12px;
  color: #999;
}

.notification-actions {
  margin-left: 8px;
}

.notification-empty {
  padding: 40px 20px;
  text-align: center;
}

.notification-footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

:deep(.ant-tabs-nav) {
  margin-bottom: 0;
}

:deep(.ant-tabs-content-holder) {
  padding: 0;
}

:deep(.ant-empty-description) {
  color: #999;
  font-size: 12px;
}
</style>
