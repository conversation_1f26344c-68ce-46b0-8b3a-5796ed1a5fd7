import Cookies from 'js-cookie'

const TOKEN_KEY = 'vulnauditbox_token'

// 获取token
export const getToken = (): string | undefined => {
  return Cookies.get(TOKEN_KEY) || localStorage.getItem(TOKEN_KEY) || undefined
}

// 设置token
export const setToken = (token: string): void => {
  // 同时存储在Cookie和localStorage中
  Cookies.set(TOKEN_KEY, token, { expires: 7 }) // 7天过期
  localStorage.setItem(TOKEN_KEY, token)
}

// 移除token
export const removeToken = (): void => {
  Cookies.remove(TOKEN_KEY)
  localStorage.removeItem(TOKEN_KEY)
}

// 检查token是否存在
export const hasToken = (): boolean => {
  return !!getToken()
}

// 解析JWT token（简单解析，不验证签名）
export const parseToken = (token?: string): any => {
  const tokenStr = token || getToken()
  if (!tokenStr) return null

  try {
    const payload = tokenStr.split('.')[1]
    const decoded = atob(payload)
    return JSON.parse(decoded)
  } catch (error) {
    console.error('Token解析失败:', error)
    return null
  }
}

// 检查token是否过期
export const isTokenExpired = (token?: string): boolean => {
  const parsed = parseToken(token)
  if (!parsed || !parsed.exp) return true

  const currentTime = Math.floor(Date.now() / 1000)
  return parsed.exp < currentTime
}

// 获取token剩余时间（秒）
export const getTokenRemainingTime = (token?: string): number => {
  const parsed = parseToken(token)
  if (!parsed || !parsed.exp) return 0

  const currentTime = Math.floor(Date.now() / 1000)
  return Math.max(0, parsed.exp - currentTime)
}
