import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { 
  Vulnerability,
  VulnerabilityCreate,
  VulnerabilityUpdateRequest,
  VulnerabilityBulkUpdate,
  VulnerabilityQueryParams,
  VulnerabilityFilterParams,
  VulnerabilityStatsResponse
} from '@/types/vulnerability'
import { vulnerabilityApi } from '@/api/vulnerability'
import { message } from 'ant-design-vue'

export const useVulnerabilityStore = defineStore('vulnerability', () => {
  const vulnerabilities = ref<Vulnerability[]>([])
  const currentVulnerability = ref<Vulnerability | null>(null)
  const statistics = ref<VulnerabilityStatsResponse | null>(null)
  const loading = ref(false)
  const total = ref(0)

  // 获取漏洞列表
  const fetchVulnerabilities = async (params?: VulnerabilityQueryParams) => {
    loading.value = true
    try {
      const response = await vulnerabilityApi.getVulnerabilities(params)
      vulnerabilities.value = response.data
      total.value = response.data.length
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取漏洞列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取过滤后的漏洞列表
  const fetchVulnerabilitiesFiltered = async (params?: VulnerabilityFilterParams) => {
    loading.value = true
    try {
      const response = await vulnerabilityApi.getVulnerabilitiesFiltered(params)
      vulnerabilities.value = response.data
      total.value = response.data.length
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取漏洞列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取漏洞详情
  const fetchVulnerability = async (id: number) => {
    loading.value = true
    try {
      const response = await vulnerabilityApi.getVulnerability(id)
      currentVulnerability.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取漏洞详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建漏洞
  const createVulnerability = async (vulnerabilityForm: VulnerabilityCreate) => {
    loading.value = true
    try {
      const response = await vulnerabilityApi.createVulnerability(vulnerabilityForm)
      vulnerabilities.value.unshift(response.data)
      message.success('漏洞创建成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '创建漏洞失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新漏洞状态
  const updateVulnerabilityStatus = async (id: number, data: VulnerabilityUpdateRequest) => {
    loading.value = true
    try {
      const response = await vulnerabilityApi.updateVulnerabilityStatus(id, data)
      
      // 更新本地状态
      const index = vulnerabilities.value.findIndex(v => v.id === id)
      if (index !== -1) {
        vulnerabilities.value[index] = response.data
      }
      
      if (currentVulnerability.value?.id === id) {
        currentVulnerability.value = response.data
      }
      
      message.success('漏洞状态更新成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '更新漏洞状态失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 批量更新漏洞
  const bulkUpdateVulnerabilities = async (data: VulnerabilityBulkUpdate) => {
    loading.value = true
    try {
      const response = await vulnerabilityApi.bulkUpdateVulnerabilities(data)
      
      // 重新获取列表以更新本地状态
      await fetchVulnerabilities()
      
      message.success(`成功更新 ${response.data.updated_count} 个漏洞`)
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '批量更新漏洞失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除漏洞
  const deleteVulnerability = async (id: number) => {
    loading.value = true
    try {
      await vulnerabilityApi.deleteVulnerability(id)
      vulnerabilities.value = vulnerabilities.value.filter(v => v.id !== id)
      
      if (currentVulnerability.value?.id === id) {
        currentVulnerability.value = null
      }
      
      message.success('漏洞删除成功')
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '删除漏洞失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取漏洞统计
  const fetchStatistics = async () => {
    try {
      const response = await vulnerabilityApi.getVulnerabilityStats()
      statistics.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取漏洞统计失败')
      throw error
    }
  }

  // 导出漏洞数据
  const exportVulnerabilities = async (params?: VulnerabilityFilterParams, format: 'csv' | 'json' | 'pdf' = 'csv') => {
    try {
      const response = await vulnerabilityApi.exportVulnerabilities(params, format)
      
      // 创建下载链接
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `vulnerabilities.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success('漏洞数据导出成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '导出漏洞数据失败')
      throw error
    }
  }

  return {
    vulnerabilities,
    currentVulnerability,
    statistics,
    loading,
    total,
    fetchVulnerabilities,
    fetchVulnerabilitiesFiltered,
    fetchVulnerability,
    createVulnerability,
    updateVulnerabilityStatus,
    bulkUpdateVulnerabilities,
    deleteVulnerability,
    fetchStatistics,
    exportVulnerabilities
  }
})
