<template>
  <div class="project-list">
    <div class="project-header">
      <h2>项目管理</h2>
      <a-button type="primary" @click="$router.push('/project/create')">
        <template #icon>
          <PlusOutlined />
        </template>
        创建项目
      </a-button>
    </div>
    
    <a-card>
      <div class="project-toolbar">
        <a-space>
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索项目名称、Git URL或Docker镜像"
            style="width: 300px"
            @search="handleSearch"
            allow-clear
          />
          <a-select
            v-model:value="typeFilter"
            placeholder="项目类型"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="git">Git项目</a-select-option>
            <a-select-option value="docker">Docker项目</a-select-option>
          </a-select>
          <a-button @click="handleRefresh" :loading="projectStore.loading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </div>
      
      <a-table
        :columns="columns"
        :data-source="projectStore.projects"
        :loading="projectStore.loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a @click="$router.push(`/project/detail/${record.id}`)">
              {{ record.name }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'git_info'">
            <div v-if="record.type === 'git'">
              <div>{{ record.git_url }}</div>
              <a-tag size="small" color="blue">{{ record.git_branch || 'main' }}</a-tag>
            </div>
            <div v-else-if="record.type === 'docker'">
              {{ record.docker_image }}
            </div>
          </template>
          
          <template v-else-if="column.key === 'current_hash'">
            <a-typography-text 
              v-if="record.current_hash" 
              :copyable="{ text: record.current_hash }"
              style="font-family: monospace; font-size: 12px;"
            >
              {{ record.current_hash.substring(0, 8) }}
            </a-typography-text>
            <span v-else class="text-gray">-</span>
          </template>
          
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a @click="$router.push(`/project/detail/${record.id}`)">查看</a>
              <a @click="handleEdit(record)">编辑</a>
              <a-popconfirm
                title="确定要删除这个项目吗？"
                @confirm="handleDelete(record.id)"
                ok-text="确定"
                cancel-text="取消"
              >
                <a style="color: #ff4d4f">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 编辑项目模态框 -->
    <a-modal
      v-model:open="showEditModal"
      title="编辑项目"
      @ok="handleUpdateProject"
      @cancel="handleCancelEdit"
      :confirm-loading="projectStore.loading"
      width="600px"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        layout="vertical"
      >
        <a-form-item label="项目名称" name="name">
          <a-input v-model:value="editForm.name" placeholder="请输入项目名称" />
        </a-form-item>
        
        <a-form-item label="项目描述" name="description">
          <a-textarea
            v-model:value="editForm.description"
            placeholder="请输入项目描述"
            :rows="3"
          />
        </a-form-item>
        
        <template v-if="editingProject?.type === 'git'">
          <a-form-item label="Git仓库URL" name="git_url">
            <a-input v-model:value="editForm.git_url" placeholder="请输入Git仓库URL" />
          </a-form-item>
          
          <a-form-item label="Git分支" name="git_branch">
            <a-input v-model:value="editForm.git_branch" placeholder="请输入Git分支（默认main）" />
          </a-form-item>
        </template>
        
        <template v-if="editingProject?.type === 'docker'">
          <a-form-item label="Docker镜像" name="docker_image">
            <a-input v-model:value="editForm.docker_image" placeholder="请输入Docker镜像" />
          </a-form-item>
        </template>
        
        <a-form-item label="源码路径" name="source_path">
          <a-input v-model:value="editForm.source_path" placeholder="请输入源码路径（可选）" />
        </a-form-item>
        
        <a-form-item label="检查间隔（分钟）" name="check_interval">
          <a-input-number 
            v-model:value="editForm.check_interval" 
            :min="1" 
            :max="10080"
            placeholder="检查间隔"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import { formatDate } from '@/utils/date'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import type { Project, UpdateProjectForm, ProjectQueryParams } from '@/types/project'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'

const router = useRouter()
const projectStore = useProjectStore()

const searchText = ref('')
const typeFilter = ref('')
const showEditModal = ref(false)
const editingProject = ref<Project | null>(null)
const editFormRef = ref<FormInstance>()

const editForm = reactive<UpdateProjectForm>({
  name: '',
  description: '',
  git_url: '',
  git_branch: '',
  docker_image: '',
  source_path: '',
  check_interval: 1440
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns: TableColumnsType = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    sorter: (a: Project, b: Project) => a.name.localeCompare(b.name)
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    filters: [
      { text: 'Git项目', value: 'git' },
      { text: 'Docker项目', value: 'docker' }
    ],
    onFilter: (value: string, record: Project) => record.type === value
  },
  {
    title: '仓库/镜像信息',
    key: 'git_info',
    width: 300,
    ellipsis: true
  },
  {
    title: '当前哈希',
    dataIndex: 'current_hash',
    key: 'current_hash',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true,
    width: 200
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
    sorter: (a: Project, b: Project) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

const editRules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 1, max: 255, message: '项目名称长度为1-255个字符', trigger: 'blur' }
  ],
  git_url: [
    { max: 512, message: 'Git URL长度不能超过512个字符', trigger: 'blur' }
  ],
  check_interval: [
    { required: true, message: '请输入检查间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 10080, message: '检查间隔范围为1-10080分钟', trigger: 'blur' }
  ]
}

const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    git: 'blue',
    docker: 'green'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    git: 'Git项目',
    docker: 'Docker项目'
  }
  return textMap[type] || type
}

const handleSearch = () => {
  fetchProjects()
}

const handleRefresh = () => {
  fetchProjects()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchProjects()
}

const handleEdit = (project: Project) => {
  editingProject.value = project
  Object.assign(editForm, {
    name: project.name,
    description: project.description,
    git_url: project.git_url,
    git_branch: project.git_branch,
    docker_image: project.docker_image,
    source_path: project.source_path,
    check_interval: project.check_interval
  })
  showEditModal.value = true
}

const handleUpdateProject = async () => {
  try {
    await editFormRef.value?.validate()
    if (editingProject.value) {
      await projectStore.updateProject(editingProject.value.id, editForm)
      showEditModal.value = false
      fetchProjects()
    }
  } catch (error) {
    console.error('Update project error:', error)
  }
}

const handleCancelEdit = () => {
  showEditModal.value = false
  editingProject.value = null
  editFormRef.value?.resetFields()
}

const handleDelete = async (id: number) => {
  try {
    await projectStore.deleteProject(id)
    fetchProjects()
  } catch (error) {
    console.error('Delete project error:', error)
  }
}

const fetchProjects = async () => {
  const params: ProjectQueryParams = {
    skip: (pagination.current - 1) * pagination.pageSize,
    limit: pagination.pageSize
  }
  
  if (searchText.value) {
    params.search = searchText.value
  }
  
  if (typeFilter.value) {
    params.project_type = typeFilter.value as 'git' | 'docker'
  }
  
  try {
    await projectStore.fetchProjects(params)
    pagination.total = projectStore.total
  } catch (error) {
    console.error('Fetch projects error:', error)
  }
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.project-header h2 {
  margin: 0;
}

.project-toolbar {
  margin-bottom: 16px;
}

.text-gray {
  color: #999;
}

:deep(.ant-table-cell) {
  padding: 12px 8px;
}
</style>
