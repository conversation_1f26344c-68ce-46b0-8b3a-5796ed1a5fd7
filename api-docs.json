﻿{
  "openapi": "3.1.0",
  "info": {
    "title": "VulnAuditBox API",
    "description": "\n    ## AI驱动的代码审计平台后端API\n\n    VulnAuditBox是一个企业级的自动化安全漏洞审计平台，提供以下核心功能：\n\n    ### 🔍 核心功能\n    - **项目管理**: 支持Git仓库和Docker镜像的项目管理\n    - **智能审计**: 基于AI的代码安全审计，支持多种扫描引擎\n    - **漏洞管理**: 全面的漏洞发现、跟踪和修复管理\n    - **报告生成**: 多格式的审计报告生成和导出\n    - **实时监控**: WebSocket实时审计进度监控\n    - **Webhook集成**: 支持第三方系统集成\n\n    ### 🚀 快速开始\n    1. 注册用户账号\n    2. 创建项目（Git或Docker）\n    3. 启动审计任务\n    4. 查看审计结果和报告\n\n    ### 📚 API文档导出\n    - OpenAPI JSON: `/api/v1/docs/openapi.json`\n    - OpenAPI YAML: `/api/v1/docs/openapi.yaml`\n    - 接口列表: `/api/v1/docs/endpoints`\n    ",
    "version": "1.0.0"
  },
  "paths": {
    "/health": {
      "get": {
        "summary": "Health Check",
        "description": "健康检查",
        "operationId": "health_check_health_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          }
        }
      }
    },
    "/api/v1/users/register": {
      "post": {
        "tags": [
          "用户管理"
        ],
        "summary": "Register User",
        "description": "用户注册\n\n- **username**: 用户名（3-50字符，只能包含字母、数字、下划线、连字符）\n- **email**: 邮箱地址\n- **password**: 密码（至少8位，包含大小写字母和数字）\n- **confirm_password**: 确认密码（可选）",
        "operationId": "register_user_api_v1_users_register_post",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UserCreate"
              }
            }
          },
          "required": true
        },
        "responses": {
          "201": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserRegisterResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/users/login": {
      "post": {
        "tags": [
          "用户管理"
        ],
        "summary": "Login User",
        "description": "用户登录\n\n- **username**: 用户名或邮箱地址\n- **password**: 密码\n\n返回JWT访问令牌和刷新令牌",
        "operationId": "login_user_api_v1_users_login_post",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UserLogin"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Token"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/users/me": {
      "get": {
        "tags": [
          "用户管理"
        ],
        "summary": "Get Current User Info",
        "description": "获取当前用户信息\n\n需要JWT认证，返回当前登录用户的详细信息",
        "operationId": "get_current_user_info_api_v1_users_me_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "put": {
        "tags": [
          "用户管理"
        ],
        "summary": "Update Current User",
        "description": "更新当前用户信息\n\n- **username**: 新用户名（可选）\n- **email**: 新邮箱地址（可选）\n- **password**: 新密码（可选）\n- **current_password**: 当前密码（修改密码时必填）",
        "operationId": "update_current_user_api_v1_users_me_put",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UserUpdate"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "delete": {
        "tags": [
          "用户管理"
        ],
        "summary": "Delete Current User",
        "description": "删除当前用户（软删除）\n\n注意：此操作不可逆，将软删除用户账户",
        "operationId": "delete_current_user_api_v1_users_me_delete",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "title": "Response Delete Current User Api V1 Users Me Delete"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/users/change-password": {
      "post": {
        "tags": [
          "用户管理"
        ],
        "summary": "Change Password",
        "description": "修改用户密码",
        "operationId": "change_password_api_v1_users_change_password_post",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/PasswordChangeRequest"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "title": "Response Change Password Api V1 Users Change Password Post"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/users/refresh-token": {
      "post": {
        "tags": [
          "用户管理"
        ],
        "summary": "Refresh Token",
        "description": "刷新访问令牌\n\n- **refresh_token**: 刷新令牌",
        "operationId": "refresh_token_api_v1_users_refresh_token_post",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/RefreshTokenRequest"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Token"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/users/profile": {
      "get": {
        "tags": [
          "用户管理"
        ],
        "summary": "Get User Profile",
        "description": "获取当前用户资料",
        "operationId": "get_user_profile_api_v1_users_profile_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "put": {
        "tags": [
          "用户管理"
        ],
        "summary": "Update User Profile",
        "description": "更新当前用户资料",
        "operationId": "update_user_profile_api_v1_users_profile_put",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UserUpdate"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UserResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/projects/": {
      "post": {
        "tags": [
          "项目管理"
        ],
        "summary": "Create Project",
        "description": "创建项目\n\n- **name**: 项目名称（必填，不能包含特殊字符）\n- **type**: 项目类型（git 或 docker）\n- **git_url**: Git仓库URL（Git项目必填）\n- **git_branch**: Git分支（可选，默认main）\n- **docker_image**: Docker镜像（Docker项目必填）\n- **source_path**: 源码路径（可选）\n- **check_interval**: 检查间隔（分钟，默认1440）\n- **description**: 项目描述（可选）\n- **extra_data**: 扩展数据（可选）\n\n自动获取Git项目的current_hash",
        "operationId": "create_project_api_v1_projects__post",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ProjectCreate"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProjectResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "get": {
        "tags": [
          "项目管理"
        ],
        "summary": "Get Projects",
        "description": "获取用户的项目列表\n\n- **skip**: 跳过的记录数\n- **limit**: 返回的记录数（最大1000）\n- **project_type**: 项目类型过滤（git 或 docker）\n- **search**: 搜索关键词（在项目名称、Git URL、Docker镜像中搜索）",
        "operationId": "get_projects_api_v1_projects__get",
        "parameters": [
          {
            "name": "skip",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 0,
              "description": "跳过的记录数",
              "default": 0,
              "title": "Skip"
            },
            "description": "跳过的记录数"
          },
          {
            "name": "limit",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 1000,
              "minimum": 1,
              "description": "返回的记录数",
              "default": 100,
              "title": "Limit"
            },
            "description": "返回的记录数"
          },
          {
            "name": "project_type",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "$ref": "#/components/schemas/ProjectType"
                },
                {
                  "type": "null"
                }
              ],
              "description": "项目类型过滤",
              "title": "Project Type"
            },
            "description": "项目类型过滤"
          },
          {
            "name": "search",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "搜索关键词",
              "title": "Search"
            },
            "description": "搜索关键词"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/ProjectResponse"
                  },
                  "title": "Response Get Projects Api V1 Projects  Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/projects/{project_id}": {
      "get": {
        "tags": [
          "项目管理"
        ],
        "summary": "Get Project",
        "description": "获取单个项目详情\n\n- **project_id**: 项目ID\n\n返回项目的完整信息，包括Git哈希、扩展数据等",
        "operationId": "get_project_api_v1_projects__project_id__get",
        "parameters": [
          {
            "name": "project_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Project Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProjectResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "put": {
        "tags": [
          "项目管理"
        ],
        "summary": "Update Project",
        "description": "更新项目信息\n\n- **project_id**: 项目ID\n- **name**: 新项目名称（可选）\n- **git_url**: 新Git仓库URL（可选，会重新获取哈希）\n- **git_branch**: 新Git分支（可选，会重新获取哈希）\n- **docker_image**: 新Docker镜像（可选）\n- **source_path**: 新源码路径（可选）\n- **check_interval**: 新检查间隔（可选）\n- **description**: 新项目描述（可选）\n\n如果更新Git URL或分支，会自动重新获取current_hash",
        "operationId": "update_project_api_v1_projects__project_id__put",
        "parameters": [
          {
            "name": "project_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Project Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ProjectUpdate"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProjectResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "delete": {
        "tags": [
          "项目管理"
        ],
        "summary": "Delete Project",
        "description": "删除项目（软删除）\n\n- **project_id**: 项目ID\n\n注意：此操作为软删除，项目数据会保留但标记为已删除",
        "operationId": "delete_project_api_v1_projects__project_id__delete",
        "parameters": [
          {
            "name": "project_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Project Id"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "Successful Response"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/projects/{project_id}/extra-data": {
      "put": {
        "tags": [
          "项目管理"
        ],
        "summary": "Update Project Extra Data",
        "description": "更新项目扩展数据\n\n- **project_id**: 项目ID\n- **extra_data**: 扩展数据字典\n\n扩展数据会与现有数据合并，支持自定义配置",
        "operationId": "update_project_extra_data_api_v1_projects__project_id__extra_data_put",
        "parameters": [
          {
            "name": "project_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Project Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ProjectExtraDataUpdate"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProjectResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/projects/{project_id}/git-info": {
      "get": {
        "tags": [
          "项目管理"
        ],
        "summary": "Get Git Repository Info",
        "description": "获取Git仓库信息\n\n- **project_id**: 项目ID\n\n返回Git仓库的详细信息，包括最新提交信息",
        "operationId": "get_git_repository_info_api_v1_projects__project_id__git_info_get",
        "parameters": [
          {
            "name": "project_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Project Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/GitRepositoryInfo"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/projects/{project_id}/refresh-hash": {
      "post": {
        "tags": [
          "项目管理"
        ],
        "summary": "Refresh Git Hash",
        "description": "刷新Git项目的哈希值\n\n- **project_id**: 项目ID\n\n手动刷新Git项目的current_hash，获取最新的提交哈希",
        "operationId": "refresh_git_hash_api_v1_projects__project_id__refresh_hash_post",
        "parameters": [
          {
            "name": "project_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Project Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProjectResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/audits/": {
      "post": {
        "tags": [
          "审计任务"
        ],
        "summary": "Create Audit",
        "description": "创建审计任务\n\n- **project_id**: 项目ID（必填）\n- **type**: 审计类型（initial 或 update）\n- **description**: 审计描述（可选）\n\n创建后自动触发Celery任务执行审计",
        "operationId": "create_audit_api_v1_audits__post",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AuditCreate"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AuditResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "get": {
        "tags": [
          "审计任务"
        ],
        "summary": "Get Audits",
        "description": "获取审计任务列表\n\n- **project_id**: 项目ID过滤（可选）\n- **status**: 状态过滤（pending/running/completed/failed）\n- **type**: 类型过滤（initial/update）\n- **skip**: 跳过的记录数\n- **limit**: 返回的记录数（最大1000）",
        "operationId": "get_audits_api_v1_audits__get",
        "parameters": [
          {
            "name": "project_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "项目ID过滤",
              "title": "Project Id"
            },
            "description": "项目ID过滤"
          },
          {
            "name": "status",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "$ref": "#/components/schemas/AuditStatus"
                },
                {
                  "type": "null"
                }
              ],
              "description": "状态过滤",
              "title": "Status"
            },
            "description": "状态过滤"
          },
          {
            "name": "type",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "$ref": "#/components/schemas/AuditType"
                },
                {
                  "type": "null"
                }
              ],
              "description": "类型过滤",
              "title": "Type"
            },
            "description": "类型过滤"
          },
          {
            "name": "skip",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 0,
              "description": "跳过的记录数",
              "default": 0,
              "title": "Skip"
            },
            "description": "跳过的记录数"
          },
          {
            "name": "limit",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 1000,
              "minimum": 1,
              "description": "返回的记录数",
              "default": 100,
              "title": "Limit"
            },
            "description": "返回的记录数"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/AuditResponse"
                  },
                  "title": "Response Get Audits Api V1 Audits  Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/audits/{audit_id}": {
      "get": {
        "tags": [
          "审计任务"
        ],
        "summary": "Get Audit",
        "description": "获取单个审计任务详情\n\n- **audit_id**: 审计ID\n\n返回审计任务的详细信息，包括状态、日志、漏洞统计等",
        "operationId": "get_audit_api_v1_audits__audit_id__get",
        "parameters": [
          {
            "name": "audit_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Audit Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AuditDetailResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "delete": {
        "tags": [
          "审计任务"
        ],
        "summary": "Delete Audit",
        "description": "删除审计任务（软删除）",
        "operationId": "delete_audit_api_v1_audits__audit_id__delete",
        "parameters": [
          {
            "name": "audit_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Audit Id"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "Successful Response"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "put": {
        "tags": [
          "审计任务"
        ],
        "summary": "Update Audit",
        "description": "更新审计任务",
        "operationId": "update_audit_api_v1_audits__audit_id__put",
        "parameters": [
          {
            "name": "audit_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Audit Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AuditUpdate"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AuditResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/audits/{audit_id}/progress": {
      "get": {
        "tags": [
          "审计任务"
        ],
        "summary": "Get Audit Progress",
        "description": "获取审计任务进度\n\n- **audit_id**: 审计ID\n\n返回审计任务的实时进度信息",
        "operationId": "get_audit_progress_api_v1_audits__audit_id__progress_get",
        "parameters": [
          {
            "name": "audit_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Audit Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AuditProgress"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/audits/{audit_id}/cancel": {
      "post": {
        "tags": [
          "审计任务"
        ],
        "summary": "Cancel Audit",
        "description": "取消审计任务\n\n- **audit_id**: 审计ID\n- **reason**: 取消原因（可选）\n\n只能取消待处理或运行中的任务",
        "operationId": "cancel_audit_api_v1_audits__audit_id__cancel_post",
        "parameters": [
          {
            "name": "audit_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Audit Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AuditCancelRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "title": "Response Cancel Audit Api V1 Audits  Audit Id  Cancel Post"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/audits/statistics": {
      "get": {
        "tags": [
          "审计任务"
        ],
        "summary": "Get Audit Statistics",
        "description": "获取审计统计信息\n\n- **project_id**: 项目ID过滤（可选）\n\n返回审计任务的统计信息",
        "operationId": "get_audit_statistics_api_v1_audits_statistics_get",
        "parameters": [
          {
            "name": "project_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "项目ID过滤",
              "title": "Project Id"
            },
            "description": "项目ID过滤"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AuditStatistics"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/vulnerabilities/": {
      "post": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Create Vulnerability",
        "description": "创建漏洞",
        "operationId": "create_vulnerability_api_v1_vulnerabilities__post",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/VulnerabilityCreate"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/VulnerabilityResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "get": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Get Vulnerabilities",
        "description": "获取漏洞列表",
        "operationId": "get_vulnerabilities_api_v1_vulnerabilities__get",
        "parameters": [
          {
            "name": "audit_id",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "description": "审计ID过滤",
              "title": "Audit Id"
            },
            "description": "审计ID过滤"
          },
          {
            "name": "severity",
            "in": "query",
            "required": false,
            "schema": {
              "type": "string",
              "description": "严重程度过滤",
              "title": "Severity"
            },
            "description": "严重程度过滤"
          },
          {
            "name": "status",
            "in": "query",
            "required": false,
            "schema": {
              "type": "string",
              "description": "状态过滤",
              "title": "Status"
            },
            "description": "状态过滤"
          },
          {
            "name": "skip",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 0,
              "description": "跳过的记录数",
              "default": 0,
              "title": "Skip"
            },
            "description": "跳过的记录数"
          },
          {
            "name": "limit",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 1000,
              "minimum": 1,
              "description": "返回的记录数",
              "default": 100,
              "title": "Limit"
            },
            "description": "返回的记录数"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/VulnerabilityResponse"
                  },
                  "title": "Response Get Vulnerabilities Api V1 Vulnerabilities  Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/vulnerabilities/{vulnerability_id}": {
      "get": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Get Vulnerability",
        "description": "获取单个漏洞",
        "operationId": "get_vulnerability_api_v1_vulnerabilities__vulnerability_id__get",
        "parameters": [
          {
            "name": "vulnerability_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Vulnerability Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/VulnerabilityResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "put": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Update Vulnerability",
        "description": "更新漏洞",
        "operationId": "update_vulnerability_api_v1_vulnerabilities__vulnerability_id__put",
        "parameters": [
          {
            "name": "vulnerability_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Vulnerability Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/VulnerabilityUpdate"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/VulnerabilityResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "delete": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Delete Vulnerability",
        "description": "删除漏洞（软删除）",
        "operationId": "delete_vulnerability_api_v1_vulnerabilities__vulnerability_id__delete",
        "parameters": [
          {
            "name": "vulnerability_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Vulnerability Id"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "Successful Response"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/vulnerabilities/filter": {
      "get": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Get Vulnerabilities Filtered",
        "description": "获取过滤后的漏洞列表",
        "operationId": "get_vulnerabilities_filtered_api_v1_vulnerabilities_filter_get",
        "parameters": [
          {
            "name": "audit_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "审计ID",
              "title": "Audit Id"
            },
            "description": "审计ID"
          },
          {
            "name": "project_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "项目ID",
              "title": "Project Id"
            },
            "description": "项目ID"
          },
          {
            "name": "severity",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "严重程度",
              "title": "Severity"
            },
            "description": "严重程度"
          },
          {
            "name": "status",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "状态",
              "title": "Status"
            },
            "description": "状态"
          },
          {
            "name": "vuln_type",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "漏洞类型",
              "title": "Vuln Type"
            },
            "description": "漏洞类型"
          },
          {
            "name": "page",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 1,
              "description": "页码",
              "default": 1,
              "title": "Page"
            },
            "description": "页码"
          },
          {
            "name": "page_size",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 100,
              "minimum": 1,
              "description": "每页大小",
              "default": 20,
              "title": "Page Size"
            },
            "description": "每页大小"
          },
          {
            "name": "sort_by",
            "in": "query",
            "required": false,
            "schema": {
              "type": "string",
              "description": "排序字段",
              "default": "created_at",
              "title": "Sort By"
            },
            "description": "排序字段"
          },
          {
            "name": "sort_order",
            "in": "query",
            "required": false,
            "schema": {
              "type": "string",
              "description": "排序方向",
              "default": "desc",
              "title": "Sort Order"
            },
            "description": "排序方向"
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/VulnerabilityResponse"
                  },
                  "title": "Response Get Vulnerabilities Filtered Api V1 Vulnerabilities Filter Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/vulnerabilities/{vulnerability_id}/status": {
      "put": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Update Vulnerability Status",
        "description": "更新漏洞状态",
        "operationId": "update_vulnerability_status_api_v1_vulnerabilities__vulnerability_id__status_put",
        "parameters": [
          {
            "name": "vulnerability_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Vulnerability Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Body_update_vulnerability_status_api_v1_vulnerabilities__vulnerability_id__status_put"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/vulnerabilities/stats/summary": {
      "get": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Get Vulnerability Statistics",
        "description": "获取漏洞统计信息",
        "operationId": "get_vulnerability_statistics_api_v1_vulnerabilities_stats_summary_get",
        "parameters": [
          {
            "name": "audit_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "审计ID",
              "title": "Audit Id"
            },
            "description": "审计ID"
          },
          {
            "name": "project_id",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "integer"
                },
                {
                  "type": "null"
                }
              ],
              "description": "项目ID",
              "title": "Project Id"
            },
            "description": "项目ID"
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/VulnerabilityStatsResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/vulnerabilities/bulk-update": {
      "post": {
        "tags": [
          "漏洞管理"
        ],
        "summary": "Bulk Update Vulnerabilities",
        "description": "批量更新漏洞状态",
        "operationId": "bulk_update_vulnerabilities_api_v1_vulnerabilities_bulk_update_post",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Body_bulk_update_vulnerabilities_api_v1_vulnerabilities_bulk_update_post"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/reports/": {
      "post": {
        "tags": [
          "报告管理"
        ],
        "summary": "Create Report",
        "description": "创建报告",
        "operationId": "create_report_api_v1_reports__post",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ReportCreate"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReportResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "get": {
        "tags": [
          "报告管理"
        ],
        "summary": "Get Reports",
        "description": "获取报告列表",
        "operationId": "get_reports_api_v1_reports__get",
        "parameters": [
          {
            "name": "audit_id",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "description": "审计ID过滤",
              "title": "Audit Id"
            },
            "description": "审计ID过滤"
          },
          {
            "name": "format",
            "in": "query",
            "required": false,
            "schema": {
              "type": "string",
              "description": "格式过滤",
              "title": "Format"
            },
            "description": "格式过滤"
          },
          {
            "name": "skip",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 0,
              "description": "跳过的记录数",
              "default": 0,
              "title": "Skip"
            },
            "description": "跳过的记录数"
          },
          {
            "name": "limit",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 1000,
              "minimum": 1,
              "description": "返回的记录数",
              "default": 100,
              "title": "Limit"
            },
            "description": "返回的记录数"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/ReportResponse"
                  },
                  "title": "Response Get Reports Api V1 Reports  Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/reports/{report_id}": {
      "get": {
        "tags": [
          "报告管理"
        ],
        "summary": "Get Report",
        "description": "获取单个报告",
        "operationId": "get_report_api_v1_reports__report_id__get",
        "parameters": [
          {
            "name": "report_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Report Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReportResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "put": {
        "tags": [
          "报告管理"
        ],
        "summary": "Update Report",
        "description": "更新报告",
        "operationId": "update_report_api_v1_reports__report_id__put",
        "parameters": [
          {
            "name": "report_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Report Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ReportUpdate"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReportResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "delete": {
        "tags": [
          "报告管理"
        ],
        "summary": "Delete Report",
        "description": "删除报告（软删除）",
        "operationId": "delete_report_api_v1_reports__report_id__delete",
        "parameters": [
          {
            "name": "report_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Report Id"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "Successful Response"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/reports/{report_id}/download": {
      "get": {
        "tags": [
          "报告管理"
        ],
        "summary": "Download Report",
        "description": "下载报告文件",
        "operationId": "download_report_api_v1_reports__report_id__download_get",
        "parameters": [
          {
            "name": "report_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Report Id"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/reports/{audit_id}/generate": {
      "post": {
        "tags": [
          "报告管理"
        ],
        "summary": "Generate Audit Report",
        "description": "生成审计报告",
        "operationId": "generate_audit_report_api_v1_reports__audit_id__generate_post",
        "parameters": [
          {
            "name": "audit_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Audit Id"
            }
          },
          {
            "name": "report_type",
            "in": "query",
            "required": false,
            "schema": {
              "type": "string",
              "default": "markdown",
              "title": "Report Type"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/webhooks/events": {
      "get": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Get Available Events",
        "description": "获取可用的Webhook事件类型",
        "operationId": "get_available_events_api_v1_webhooks_events_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebhookEventsListResponse"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/webhooks/": {
      "post": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Create Webhook",
        "description": "创建Webhook配置",
        "operationId": "create_webhook_api_v1_webhooks__post",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Body_create_webhook_api_v1_webhooks__post"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebhookResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "get": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Get Webhooks",
        "description": "获取用户的Webhook列表",
        "operationId": "get_webhooks_api_v1_webhooks__get",
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 1,
              "description": "页码",
              "default": 1,
              "title": "Page"
            },
            "description": "页码"
          },
          {
            "name": "page_size",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 100,
              "minimum": 1,
              "description": "每页大小",
              "default": 20,
              "title": "Page Size"
            },
            "description": "每页大小"
          },
          {
            "name": "is_active",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "boolean"
                },
                {
                  "type": "null"
                }
              ],
              "description": "是否启用",
              "title": "Is Active"
            },
            "description": "是否启用"
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebhookResponse"
                  },
                  "title": "Response Get Webhooks Api V1 Webhooks  Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/webhooks/{webhook_id}": {
      "get": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Get Webhook",
        "description": "获取单个Webhook详情",
        "operationId": "get_webhook_api_v1_webhooks__webhook_id__get",
        "parameters": [
          {
            "name": "webhook_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Webhook Id"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebhookResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "put": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Update Webhook",
        "description": "更新Webhook配置",
        "operationId": "update_webhook_api_v1_webhooks__webhook_id__put",
        "parameters": [
          {
            "name": "webhook_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Webhook Id"
            }
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/Body_update_webhook_api_v1_webhooks__webhook_id__put"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebhookResponse"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      },
      "delete": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Delete Webhook",
        "description": "删除Webhook配置",
        "operationId": "delete_webhook_api_v1_webhooks__webhook_id__delete",
        "parameters": [
          {
            "name": "webhook_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Webhook Id"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/webhooks/{webhook_id}/test": {
      "post": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Test Webhook Endpoint",
        "description": "测试Webhook连接",
        "operationId": "test_webhook_endpoint_api_v1_webhooks__webhook_id__test_post",
        "parameters": [
          {
            "name": "webhook_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Webhook Id"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/webhooks/{webhook_id}/events": {
      "get": {
        "tags": [
          "Webhook管理",
          "webhooks"
        ],
        "summary": "Get Webhook Events",
        "description": "获取Webhook事件历史",
        "operationId": "get_webhook_events_api_v1_webhooks__webhook_id__events_get",
        "parameters": [
          {
            "name": "webhook_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "title": "Webhook Id"
            }
          },
          {
            "name": "page",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "minimum": 1,
              "description": "页码",
              "default": 1,
              "title": "Page"
            },
            "description": "页码"
          },
          {
            "name": "page_size",
            "in": "query",
            "required": false,
            "schema": {
              "type": "integer",
              "maximum": 100,
              "minimum": 1,
              "description": "每页大小",
              "default": 20,
              "title": "Page Size"
            },
            "description": "每页大小"
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebhookEventResponse"
                  },
                  "title": "Response Get Webhook Events Api V1 Webhooks  Webhook Id  Events Get"
                }
              }
            }
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        },
        "security": [
          {
            "BearerAuth": []
          }
        ]
      }
    },
    "/api/v1/docs/": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Api Documentation",
        "description": "获取API文档概览\n\n返回API的基本信息、端点统计和分组信息，用于前端展示API文档首页。",
        "operationId": "get_api_documentation_api_v1_docs__get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/APIDocumentationResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not found"
          }
        }
      }
    },
    "/api/v1/docs/endpoints": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Endpoints List",
        "description": "获取API端点列表\n\n支持按标签、HTTP方法过滤，可选择是否包含已废弃的端点。",
        "operationId": "get_endpoints_list_api_v1_docs_endpoints_get",
        "parameters": [
          {
            "name": "tag",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "按标签过滤",
              "title": "Tag"
            },
            "description": "按标签过滤"
          },
          {
            "name": "method",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "按HTTP方法过滤",
              "title": "Method"
            },
            "description": "按HTTP方法过滤"
          },
          {
            "name": "include_deprecated",
            "in": "query",
            "required": false,
            "schema": {
              "type": "boolean",
              "description": "是否包含已废弃的端点",
              "default": true,
              "title": "Include Deprecated"
            },
            "description": "是否包含已废弃的端点"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/EndpointListResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not found"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/docs/openapi.json": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Openapi Json",
        "description": "获取OpenAPI JSON格式规范\n\n返回完整的OpenAPI 3.0规范，可用于生成客户端SDK或导入到API测试工具。",
        "operationId": "get_openapi_json_api_v1_docs_openapi_json_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "404": {
            "description": "Not found"
          }
        }
      }
    },
    "/api/v1/docs/openapi.yaml": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Openapi Yaml",
        "description": "获取OpenAPI YAML格式规范\n\n返回YAML格式的OpenAPI规范，更适合人类阅读和版本控制。",
        "operationId": "get_openapi_yaml_api_v1_docs_openapi_yaml_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "404": {
            "description": "Not found"
          }
        }
      }
    },
    "/api/v1/docs/export": {
      "post": {
        "tags": [
          "接口文档"
        ],
        "summary": "Export Documentation",
        "description": "自定义导出API文档\n\n支持多种格式导出，可自定义包含的内容和过滤条件。",
        "operationId": "export_documentation_api_v1_docs_export_post",
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ExportRequest"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ExportResponse"
                }
              }
            }
          },
          "404": {
            "description": "Not found"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/docs/statistics": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Api Statistics",
        "description": "获取API统计信息\n\n返回API端点的统计数据，包括总数、按方法分组、按标签分组等信息。",
        "operationId": "get_api_statistics_api_v1_docs_statistics_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/APIStatistics"
                }
              }
            }
          },
          "404": {
            "description": "Not found"
          }
        }
      }
    },
    "/api/v1/docs/frontend-integration": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Frontend Integration Info",
        "description": "获取前端集成信息\n\n返回前端开发所需的集成信息，包括认证方式、CORS配置、速率限制等。",
        "operationId": "get_frontend_integration_info_api_v1_docs_frontend_integration_get",
        "parameters": [
          {
            "name": "base_url",
            "in": "query",
            "required": false,
            "schema": {
              "type": "string",
              "description": "API基础URL",
              "default": "http://localhost:8000",
              "title": "Base Url"
            },
            "description": "API基础URL"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/FrontendIntegrationInfo"
                }
              }
            }
          },
          "404": {
            "description": "Not found"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/docs/examples": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Api Examples",
        "description": "获取API调用示例\n\n返回各种编程语言的API调用示例，包括cURL、JavaScript、Python等。",
        "operationId": "get_api_examples_api_v1_docs_examples_get",
        "parameters": [
          {
            "name": "endpoint",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "指定端点路径",
              "title": "Endpoint"
            },
            "description": "指定端点路径"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/APIExample"
                  },
                  "title": "Response Get Api Examples Api V1 Docs Examples Get"
                }
              }
            }
          },
          "404": {
            "description": "Not found"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/docs/markdown": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Get Markdown Documentation",
        "description": "获取Markdown格式的API文档\n\n返回适合在GitHub、GitLab等平台展示的Markdown格式文档。",
        "operationId": "get_markdown_documentation_api_v1_docs_markdown_get",
        "parameters": [
          {
            "name": "include_deprecated",
            "in": "query",
            "required": false,
            "schema": {
              "type": "boolean",
              "description": "是否包含已废弃的端点",
              "default": false,
              "title": "Include Deprecated"
            },
            "description": "是否包含已废弃的端点"
          },
          {
            "name": "tags",
            "in": "query",
            "required": false,
            "schema": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "null"
                }
              ],
              "description": "指定标签，多个用逗号分隔",
              "title": "Tags"
            },
            "description": "指定标签，多个用逗号分隔"
          }
        ],
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "404": {
            "description": "Not found"
          },
          "422": {
            "description": "Validation Error",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/HTTPValidationError"
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/docs/refresh-cache": {
      "post": {
        "tags": [
          "接口文档"
        ],
        "summary": "Refresh Documentation Cache",
        "description": "刷新文档缓存\n\n手动刷新API文档缓存，确保获取最新的接口信息。",
        "operationId": "refresh_documentation_cache_api_v1_docs_refresh_cache_post",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "404": {
            "description": "Not found"
          }
        }
      }
    },
    "/api/v1/docs/health": {
      "get": {
        "tags": [
          "接口文档"
        ],
        "summary": "Docs Health Check",
        "description": "文档服务健康检查\n\n检查文档服务是否正常运行。",
        "operationId": "docs_health_check_api_v1_docs_health_get",
        "responses": {
          "200": {
            "description": "Successful Response",
            "content": {
              "application/json": {
                "schema": {}
              }
            }
          },
          "404": {
            "description": "Not found"
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "APIDocumentationResponse": {
        "properties": {
          "title": {
            "type": "string",
            "title": "Title",
            "description": "API标题"
          },
          "version": {
            "type": "string",
            "title": "Version",
            "description": "API版本"
          },
          "description": {
            "type": "string",
            "title": "Description",
            "description": "API描述"
          },
          "total_endpoints": {
            "type": "integer",
            "title": "Total Endpoints",
            "description": "总端点数"
          },
          "groups": {
            "items": {
              "$ref": "#/components/schemas/APIGroup"
            },
            "type": "array",
            "title": "Groups",
            "description": "API分组"
          },
          "export_formats": {
            "items": {
              "type": "string"
            },
            "type": "array",
            "title": "Export Formats",
            "description": "支持的导出格式"
          }
        },
        "type": "object",
        "required": [
          "title",
          "version",
          "description",
          "total_endpoints",
          "groups",
          "export_formats"
        ],
        "title": "APIDocumentationResponse",
        "description": "API文档响应"
      },
      "APIEndpoint": {
        "properties": {
          "path": {
            "type": "string",
            "title": "Path",
            "description": "API路径"
          },
          "method": {
            "type": "string",
            "title": "Method",
            "description": "HTTP方法"
          },
          "summary": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Summary",
            "description": "接口摘要"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "接口描述"
          },
          "tags": {
            "items": {
              "type": "string"
            },
            "type": "array",
            "title": "Tags",
            "description": "接口标签"
          },
          "parameters": {
            "items": {
              "type": "object"
            },
            "type": "array",
            "title": "Parameters",
            "description": "请求参数"
          },
          "request_body": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Request Body",
            "description": "请求体结构"
          },
          "responses": {
            "additionalProperties": {
              "type": "object"
            },
            "type": "object",
            "title": "Responses",
            "description": "响应结构"
          },
          "deprecated": {
            "type": "boolean",
            "title": "Deprecated",
            "description": "是否已废弃",
            "default": false
          }
        },
        "type": "object",
        "required": [
          "path",
          "method"
        ],
        "title": "APIEndpoint",
        "description": "API端点信息"
      },
      "APIExample": {
        "properties": {
          "endpoint": {
            "type": "string",
            "title": "Endpoint",
            "description": "端点路径"
          },
          "method": {
            "type": "string",
            "title": "Method",
            "description": "HTTP方法"
          },
          "request_example": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Request Example",
            "description": "请求示例"
          },
          "response_example": {
            "type": "object",
            "title": "Response Example",
            "description": "响应示例"
          },
          "curl_example": {
            "type": "string",
            "title": "Curl Example",
            "description": "cURL示例"
          },
          "javascript_example": {
            "type": "string",
            "title": "Javascript Example",
            "description": "JavaScript示例"
          },
          "python_example": {
            "type": "string",
            "title": "Python Example",
            "description": "Python示例"
          }
        },
        "type": "object",
        "required": [
          "endpoint",
          "method",
          "response_example",
          "curl_example",
          "javascript_example",
          "python_example"
        ],
        "title": "APIExample",
        "description": "API示例"
      },
      "APIGroup": {
        "properties": {
          "name": {
            "type": "string",
            "title": "Name",
            "description": "分组名称"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "分组描述"
          },
          "endpoints": {
            "items": {
              "$ref": "#/components/schemas/APIEndpoint"
            },
            "type": "array",
            "title": "Endpoints",
            "description": "端点列表"
          }
        },
        "type": "object",
        "required": [
          "name"
        ],
        "title": "APIGroup",
        "description": "API分组信息"
      },
      "APIStatistics": {
        "properties": {
          "total_endpoints": {
            "type": "integer",
            "title": "Total Endpoints",
            "description": "总端点数"
          },
          "endpoints_by_method": {
            "additionalProperties": {
              "type": "integer"
            },
            "type": "object",
            "title": "Endpoints By Method",
            "description": "按方法分组的端点数"
          },
          "endpoints_by_tag": {
            "additionalProperties": {
              "type": "integer"
            },
            "type": "object",
            "title": "Endpoints By Tag",
            "description": "按标签分组的端点数"
          },
          "deprecated_count": {
            "type": "integer",
            "title": "Deprecated Count",
            "description": "已废弃的端点数"
          },
          "last_updated": {
            "type": "string",
            "title": "Last Updated",
            "description": "最后更新时间"
          }
        },
        "type": "object",
        "required": [
          "total_endpoints",
          "endpoints_by_method",
          "endpoints_by_tag",
          "deprecated_count",
          "last_updated"
        ],
        "title": "APIStatistics",
        "description": "API统计信息"
      },
      "AuditCancelRequest": {
        "properties": {
          "reason": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 200
              },
              {
                "type": "null"
              }
            ],
            "title": "Reason",
            "description": "取消原因"
          }
        },
        "type": "object",
        "title": "AuditCancelRequest",
        "description": "取消审计请求schema"
      },
      "AuditCreate": {
        "properties": {
          "type": {
            "allOf": [
              {
                "$ref": "#/components/schemas/AuditType"
              }
            ],
            "description": "审计类型"
          },
          "project_id": {
            "type": "integer",
            "exclusiveMinimum": 0.0,
            "title": "Project Id",
            "description": "项目ID"
          },
          "description": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 500
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "审计描述"
          }
        },
        "type": "object",
        "required": [
          "type",
          "project_id"
        ],
        "title": "AuditCreate",
        "description": "创建审计schema"
      },
      "AuditDetailResponse": {
        "properties": {
          "type": {
            "allOf": [
              {
                "$ref": "#/components/schemas/AuditType"
              }
            ],
            "description": "审计类型"
          },
          "id": {
            "type": "integer",
            "title": "Id",
            "description": "审计ID"
          },
          "project_id": {
            "type": "integer",
            "title": "Project Id",
            "description": "项目ID"
          },
          "status": {
            "allOf": [
              {
                "$ref": "#/components/schemas/AuditStatus"
              }
            ],
            "description": "审计状态"
          },
          "start_time": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Start Time",
            "description": "开始时间"
          },
          "end_time": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "End Time",
            "description": "结束时间"
          },
          "diff_content": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Diff Content",
            "description": "差异内容"
          },
          "error_message": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Error Message",
            "description": "错误信息"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "title": "Created At",
            "description": "创建时间"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Updated At",
            "description": "更新时间"
          },
          "deleted_at": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Deleted At",
            "description": "删除时间"
          },
          "extra_data": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Extra Data",
            "description": "扩展数据"
          },
          "project_name": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Project Name",
            "description": "项目名称"
          },
          "vulnerability_count": {
            "type": "integer",
            "title": "Vulnerability Count",
            "description": "漏洞数量",
            "default": 0
          },
          "logs": {
            "items": {
              "$ref": "#/components/schemas/AuditLog"
            },
            "type": "array",
            "title": "Logs",
            "description": "审计日志"
          }
        },
        "type": "object",
        "required": [
          "type",
          "id",
          "project_id",
          "status",
          "created_at",
          "updated_at"
        ],
        "title": "AuditDetailResponse",
        "description": "审计详情响应schema"
      },
      "AuditLog": {
        "properties": {
          "timestamp": {
            "type": "string",
            "format": "date-time",
            "title": "Timestamp",
            "description": "时间戳"
          },
          "level": {
            "type": "string",
            "title": "Level",
            "description": "日志级别"
          },
          "message": {
            "type": "string",
            "title": "Message",
            "description": "日志消息"
          },
          "step": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Step",
            "description": "执行步骤"
          }
        },
        "type": "object",
        "required": [
          "timestamp",
          "level",
          "message"
        ],
        "title": "AuditLog",
        "description": "审计日志schema"
      },
      "AuditProgress": {
        "properties": {
          "audit_id": {
            "type": "integer",
            "title": "Audit Id",
            "description": "审计ID"
          },
          "status": {
            "allOf": [
              {
                "$ref": "#/components/schemas/AuditStatus"
              }
            ],
            "description": "当前状态"
          },
          "progress": {
            "type": "integer",
            "maximum": 100.0,
            "minimum": 0.0,
            "title": "Progress",
            "description": "进度百分比"
          },
          "current_step": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Current Step",
            "description": "当前步骤"
          },
          "message": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Message",
            "description": "进度消息"
          },
          "estimated_remaining": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Estimated Remaining",
            "description": "预计剩余时间（秒）"
          }
        },
        "type": "object",
        "required": [
          "audit_id",
          "status",
          "progress"
        ],
        "title": "AuditProgress",
        "description": "审计进度schema"
      },
      "AuditResponse": {
        "properties": {
          "type": {
            "allOf": [
              {
                "$ref": "#/components/schemas/AuditType"
              }
            ],
            "description": "审计类型"
          },
          "id": {
            "type": "integer",
            "title": "Id",
            "description": "审计ID"
          },
          "project_id": {
            "type": "integer",
            "title": "Project Id",
            "description": "项目ID"
          },
          "status": {
            "allOf": [
              {
                "$ref": "#/components/schemas/AuditStatus"
              }
            ],
            "description": "审计状态"
          },
          "start_time": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Start Time",
            "description": "开始时间"
          },
          "end_time": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "End Time",
            "description": "结束时间"
          },
          "diff_content": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Diff Content",
            "description": "差异内容"
          },
          "error_message": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Error Message",
            "description": "错误信息"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "title": "Created At",
            "description": "创建时间"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Updated At",
            "description": "更新时间"
          },
          "deleted_at": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Deleted At",
            "description": "删除时间"
          },
          "extra_data": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Extra Data",
            "description": "扩展数据"
          }
        },
        "type": "object",
        "required": [
          "type",
          "id",
          "project_id",
          "status",
          "created_at",
          "updated_at"
        ],
        "title": "AuditResponse",
        "description": "审计响应schema"
      },
      "AuditStatistics": {
        "properties": {
          "total_audits": {
            "type": "integer",
            "title": "Total Audits",
            "description": "总审计数"
          },
          "pending_audits": {
            "type": "integer",
            "title": "Pending Audits",
            "description": "待处理审计数"
          },
          "running_audits": {
            "type": "integer",
            "title": "Running Audits",
            "description": "运行中审计数"
          },
          "completed_audits": {
            "type": "integer",
            "title": "Completed Audits",
            "description": "已完成审计数"
          },
          "failed_audits": {
            "type": "integer",
            "title": "Failed Audits",
            "description": "失败审计数"
          },
          "total_vulnerabilities": {
            "type": "integer",
            "title": "Total Vulnerabilities",
            "description": "总漏洞数"
          },
          "last_audit_time": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Last Audit Time",
            "description": "最后审计时间"
          }
        },
        "type": "object",
        "required": [
          "total_audits",
          "pending_audits",
          "running_audits",
          "completed_audits",
          "failed_audits",
          "total_vulnerabilities"
        ],
        "title": "AuditStatistics",
        "description": "审计统计schema"
      },
      "AuditStatus": {
        "type": "string",
        "enum": [
          "pending",
          "running",
          "completed",
          "failed"
        ],
        "title": "AuditStatus",
        "description": "审计状态枚举"
      },
      "AuditType": {
        "type": "string",
        "enum": [
          "initial",
          "update"
        ],
        "title": "AuditType",
        "description": "审计类型枚举"
      },
      "AuditUpdate": {
        "properties": {
          "status": {
            "anyOf": [
              {
                "$ref": "#/components/schemas/AuditStatus"
              },
              {
                "type": "null"
              }
            ],
            "description": "审计状态"
          },
          "diff_content": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Diff Content",
            "description": "差异内容"
          },
          "error_message": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 1000
              },
              {
                "type": "null"
              }
            ],
            "title": "Error Message",
            "description": "错误信息"
          },
          "progress": {
            "anyOf": [
              {
                "type": "integer",
                "maximum": 100.0,
                "minimum": 0.0
              },
              {
                "type": "null"
              }
            ],
            "title": "Progress",
            "description": "进度百分比"
          }
        },
        "type": "object",
        "title": "AuditUpdate",
        "description": "更新审计schema"
      },
      "Body_bulk_update_vulnerabilities_api_v1_vulnerabilities_bulk_update_post": {
        "properties": {
          "update_request": {
            "$ref": "#/components/schemas/BulkUpdateRequest"
          },
          "credentials": {
            "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
          }
        },
        "type": "object",
        "required": [
          "update_request"
        ],
        "title": "Body_bulk_update_vulnerabilities_api_v1_vulnerabilities_bulk_update_post"
      },
      "Body_create_webhook_api_v1_webhooks__post": {
        "properties": {
          "webhook_data": {
            "$ref": "#/components/schemas/WebhookCreateRequest"
          },
          "credentials": {
            "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
          }
        },
        "type": "object",
        "required": [
          "webhook_data"
        ],
        "title": "Body_create_webhook_api_v1_webhooks__post"
      },
      "Body_update_vulnerability_status_api_v1_vulnerabilities__vulnerability_id__status_put": {
        "properties": {
          "update_request": {
            "$ref": "#/components/schemas/VulnerabilityUpdateRequest"
          },
          "credentials": {
            "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
          }
        },
        "type": "object",
        "required": [
          "update_request"
        ],
        "title": "Body_update_vulnerability_status_api_v1_vulnerabilities__vulnerability_id__status_put"
      },
      "Body_update_webhook_api_v1_webhooks__webhook_id__put": {
        "properties": {
          "webhook_data": {
            "$ref": "#/components/schemas/WebhookUpdateRequest"
          },
          "credentials": {
            "$ref": "#/components/schemas/HTTPAuthorizationCredentials"
          }
        },
        "type": "object",
        "required": [
          "webhook_data"
        ],
        "title": "Body_update_webhook_api_v1_webhooks__webhook_id__put"
      },
      "BulkUpdateRequest": {
        "properties": {
          "vulnerability_ids": {
            "items": {
              "type": "integer"
            },
            "type": "array",
            "title": "Vulnerability Ids",
            "description": "漏洞ID列表"
          },
          "status": {
            "type": "string",
            "title": "Status",
            "description": "新状态"
          },
          "notes": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Notes",
            "description": "更新备注"
          }
        },
        "type": "object",
        "required": [
          "vulnerability_ids",
          "status"
        ],
        "title": "BulkUpdateRequest"
      },
      "EndpointListResponse": {
        "properties": {
          "total": {
            "type": "integer",
            "title": "Total",
            "description": "总数"
          },
          "endpoints": {
            "items": {
              "$ref": "#/components/schemas/APIEndpoint"
            },
            "type": "array",
            "title": "Endpoints",
            "description": "端点列表"
          }
        },
        "type": "object",
        "required": [
          "total",
          "endpoints"
        ],
        "title": "EndpointListResponse",
        "description": "端点列表响应"
      },
      "ExportFormat": {
        "type": "string",
        "enum": [
          "json",
          "yaml",
          "markdown"
        ],
        "title": "ExportFormat",
        "description": "导出格式枚举"
      },
      "ExportRequest": {
        "properties": {
          "format": {
            "allOf": [
              {
                "$ref": "#/components/schemas/ExportFormat"
              }
            ],
            "description": "导出格式"
          },
          "include_examples": {
            "type": "boolean",
            "title": "Include Examples",
            "description": "是否包含示例",
            "default": true
          },
          "include_deprecated": {
            "type": "boolean",
            "title": "Include Deprecated",
            "description": "是否包含已废弃的接口",
            "default": false
          },
          "tags": {
            "anyOf": [
              {
                "items": {
                  "type": "string"
                },
                "type": "array"
              },
              {
                "type": "null"
              }
            ],
            "title": "Tags",
            "description": "指定导出的标签"
          }
        },
        "type": "object",
        "required": [
          "format"
        ],
        "title": "ExportRequest",
        "description": "导出请求"
      },
      "ExportResponse": {
        "properties": {
          "format": {
            "type": "string",
            "title": "Format",
            "description": "导出格式"
          },
          "content": {
            "type": "string",
            "title": "Content",
            "description": "导出内容"
          },
          "filename": {
            "type": "string",
            "title": "Filename",
            "description": "建议的文件名"
          },
          "size": {
            "type": "integer",
            "title": "Size",
            "description": "内容大小（字节）"
          }
        },
        "type": "object",
        "required": [
          "format",
          "content",
          "filename",
          "size"
        ],
        "title": "ExportResponse",
        "description": "导出响应"
      },
      "FrontendIntegrationInfo": {
        "properties": {
          "base_url": {
            "type": "string",
            "title": "Base Url",
            "description": "API基础URL"
          },
          "auth_type": {
            "type": "string",
            "title": "Auth Type",
            "description": "认证类型"
          },
          "auth_header": {
            "type": "string",
            "title": "Auth Header",
            "description": "认证头名称"
          },
          "cors_enabled": {
            "type": "boolean",
            "title": "Cors Enabled",
            "description": "是否启用CORS"
          },
          "rate_limit": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Rate Limit",
            "description": "速率限制信息"
          },
          "websocket_endpoints": {
            "items": {
              "type": "string"
            },
            "type": "array",
            "title": "Websocket Endpoints",
            "description": "WebSocket端点"
          }
        },
        "type": "object",
        "required": [
          "base_url",
          "auth_type",
          "auth_header",
          "cors_enabled"
        ],
        "title": "FrontendIntegrationInfo",
        "description": "前端集成信息"
      },
      "GitRepositoryInfo": {
        "properties": {
          "url": {
            "type": "string",
            "title": "Url",
            "description": "仓库URL"
          },
          "branch": {
            "type": "string",
            "title": "Branch",
            "description": "分支名称"
          },
          "current_hash": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Current Hash",
            "description": "当前哈希"
          },
          "last_commit_message": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Last Commit Message",
            "description": "最后提交信息"
          },
          "last_commit_author": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Last Commit Author",
            "description": "最后提交作者"
          },
          "last_commit_date": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Last Commit Date",
            "description": "最后提交时间"
          }
        },
        "type": "object",
        "required": [
          "url",
          "branch"
        ],
        "title": "GitRepositoryInfo",
        "description": "Git仓库信息schema"
      },
      "HTTPAuthorizationCredentials": {
        "properties": {
          "scheme": {
            "type": "string",
            "title": "Scheme"
          },
          "credentials": {
            "type": "string",
            "title": "Credentials"
          }
        },
        "type": "object",
        "required": [
          "scheme",
          "credentials"
        ],
        "title": "HTTPAuthorizationCredentials",
        "description": "The HTTP authorization credentials in the result of using `HTTPBearer` or\n`HTTPDigest` in a dependency.\n\nThe HTTP authorization header value is split by the first space.\n\nThe first part is the `scheme`, the second part is the `credentials`.\n\nFor example, in an HTTP Bearer token scheme, the client will send a header\nlike:\n\n```\nAuthorization: Bearer deadbeef12346\n```\n\nIn this case:\n\n* `scheme` will have the value `\"Bearer\"`\n* `credentials` will have the value `\"deadbeef12346\"`"
      },
      "HTTPValidationError": {
        "properties": {
          "detail": {
            "items": {
              "$ref": "#/components/schemas/ValidationError"
            },
            "type": "array",
            "title": "Detail"
          }
        },
        "type": "object",
        "title": "HTTPValidationError"
      },
      "PasswordChangeRequest": {
        "properties": {
          "current_password": {
            "type": "string",
            "title": "Current Password",
            "description": "当前密码"
          },
          "new_password": {
            "type": "string",
            "maxLength": 128,
            "minLength": 8,
            "title": "New Password",
            "description": "新密码"
          },
          "confirm_password": {
            "type": "string",
            "title": "Confirm Password",
            "description": "确认新密码"
          }
        },
        "type": "object",
        "required": [
          "current_password",
          "new_password",
          "confirm_password"
        ],
        "title": "PasswordChangeRequest",
        "description": "密码修改请求schema"
      },
      "ProjectCreate": {
        "properties": {
          "name": {
            "type": "string",
            "maxLength": 255,
            "minLength": 1,
            "title": "Name",
            "description": "项目名称"
          },
          "type": {
            "allOf": [
              {
                "$ref": "#/components/schemas/ProjectType"
              }
            ],
            "description": "项目类型"
          },
          "git_url": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 512
              },
              {
                "type": "null"
              }
            ],
            "title": "Git Url",
            "description": "Git仓库URL"
          },
          "git_branch": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 255
              },
              {
                "type": "null"
              }
            ],
            "title": "Git Branch",
            "description": "Git分支"
          },
          "docker_image": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 255
              },
              {
                "type": "null"
              }
            ],
            "title": "Docker Image",
            "description": "Docker镜像"
          },
          "source_path": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 512
              },
              {
                "type": "null"
              }
            ],
            "title": "Source Path",
            "description": "源码路径"
          },
          "check_interval": {
            "anyOf": [
              {
                "type": "integer",
                "maximum": 10080.0,
                "minimum": 60.0
              },
              {
                "type": "null"
              }
            ],
            "title": "Check Interval",
            "description": "检查间隔(分钟)",
            "default": 1440
          },
          "extra_data": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Extra Data",
            "description": "扩展数据"
          },
          "description": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 1000
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "项目描述"
          }
        },
        "type": "object",
        "required": [
          "name",
          "type"
        ],
        "title": "ProjectCreate",
        "description": "创建项目schema"
      },
      "ProjectExtraDataUpdate": {
        "properties": {
          "extra_data": {
            "type": "object",
            "title": "Extra Data",
            "description": "扩展数据"
          }
        },
        "type": "object",
        "required": [
          "extra_data"
        ],
        "title": "ProjectExtraDataUpdate",
        "description": "项目扩展数据更新schema"
      },
      "ProjectResponse": {
        "properties": {
          "name": {
            "type": "string",
            "maxLength": 255,
            "minLength": 1,
            "title": "Name",
            "description": "项目名称"
          },
          "type": {
            "allOf": [
              {
                "$ref": "#/components/schemas/ProjectType"
              }
            ],
            "description": "项目类型"
          },
          "id": {
            "type": "integer",
            "title": "Id",
            "description": "项目ID"
          },
          "user_id": {
            "type": "integer",
            "title": "User Id",
            "description": "用户ID"
          },
          "git_url": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Git Url",
            "description": "Git仓库URL"
          },
          "git_branch": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Git Branch",
            "description": "Git分支"
          },
          "current_hash": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Current Hash",
            "description": "当前Git哈希"
          },
          "docker_image": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Docker Image",
            "description": "Docker镜像"
          },
          "source_path": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Source Path",
            "description": "源码路径"
          },
          "check_interval": {
            "type": "integer",
            "title": "Check Interval",
            "description": "检查间隔(分钟)"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "项目描述"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "title": "Created At",
            "description": "创建时间"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Updated At",
            "description": "更新时间"
          },
          "deleted_at": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Deleted At",
            "description": "删除时间"
          },
          "extra_data": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Extra Data",
            "description": "扩展数据"
          }
        },
        "type": "object",
        "required": [
          "name",
          "type",
          "id",
          "user_id",
          "check_interval",
          "created_at",
          "updated_at"
        ],
        "title": "ProjectResponse",
        "description": "项目响应schema"
      },
      "ProjectType": {
        "type": "string",
        "enum": [
          "git",
          "docker"
        ],
        "title": "ProjectType",
        "description": "项目类型枚举"
      },
      "ProjectUpdate": {
        "properties": {
          "name": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 255,
                "minLength": 1
              },
              {
                "type": "null"
              }
            ],
            "title": "Name",
            "description": "项目名称"
          },
          "git_url": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 512
              },
              {
                "type": "null"
              }
            ],
            "title": "Git Url",
            "description": "Git仓库URL"
          },
          "git_branch": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 255
              },
              {
                "type": "null"
              }
            ],
            "title": "Git Branch",
            "description": "Git分支"
          },
          "docker_image": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 255
              },
              {
                "type": "null"
              }
            ],
            "title": "Docker Image",
            "description": "Docker镜像"
          },
          "source_path": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 512
              },
              {
                "type": "null"
              }
            ],
            "title": "Source Path",
            "description": "源码路径"
          },
          "check_interval": {
            "anyOf": [
              {
                "type": "integer",
                "maximum": 10080.0,
                "minimum": 60.0
              },
              {
                "type": "null"
              }
            ],
            "title": "Check Interval",
            "description": "检查间隔(分钟)"
          },
          "description": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 1000
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "项目描述"
          }
        },
        "type": "object",
        "title": "ProjectUpdate",
        "description": "更新项目schema"
      },
      "RefreshTokenRequest": {
        "properties": {
          "refresh_token": {
            "type": "string",
            "title": "Refresh Token",
            "description": "刷新令牌"
          }
        },
        "type": "object",
        "required": [
          "refresh_token"
        ],
        "title": "RefreshTokenRequest",
        "description": "刷新令牌请求schema"
      },
      "ReportCreate": {
        "properties": {
          "format": {
            "allOf": [
              {
                "$ref": "#/components/schemas/ReportFormat"
              }
            ],
            "description": "报告格式"
          },
          "content": {
            "type": "string",
            "title": "Content",
            "description": "报告内容"
          },
          "audit_id": {
            "type": "integer",
            "title": "Audit Id",
            "description": "审计ID"
          }
        },
        "type": "object",
        "required": [
          "format",
          "content",
          "audit_id"
        ],
        "title": "ReportCreate",
        "description": "创建报告schema"
      },
      "ReportFormat": {
        "type": "string",
        "enum": [
          "markdown",
          "pdf"
        ],
        "title": "ReportFormat",
        "description": "报告格式枚举"
      },
      "ReportResponse": {
        "properties": {
          "format": {
            "allOf": [
              {
                "$ref": "#/components/schemas/ReportFormat"
              }
            ],
            "description": "报告格式"
          },
          "content": {
            "type": "string",
            "title": "Content",
            "description": "报告内容"
          },
          "id": {
            "type": "integer",
            "title": "Id",
            "description": "报告ID"
          },
          "audit_id": {
            "type": "integer",
            "title": "Audit Id",
            "description": "审计ID"
          },
          "generated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Generated At",
            "description": "生成时间"
          },
          "notified": {
            "type": "boolean",
            "title": "Notified",
            "description": "是否已通知"
          },
          "deleted_at": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Deleted At",
            "description": "删除时间"
          },
          "extra_data": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Extra Data",
            "description": "扩展数据"
          }
        },
        "type": "object",
        "required": [
          "format",
          "content",
          "id",
          "audit_id",
          "generated_at",
          "notified"
        ],
        "title": "ReportResponse",
        "description": "报告响应schema"
      },
      "ReportUpdate": {
        "properties": {
          "content": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Content",
            "description": "报告内容"
          },
          "notified": {
            "anyOf": [
              {
                "type": "boolean"
              },
              {
                "type": "null"
              }
            ],
            "title": "Notified",
            "description": "是否已通知"
          }
        },
        "type": "object",
        "title": "ReportUpdate",
        "description": "更新报告schema"
      },
      "Token": {
        "properties": {
          "access_token": {
            "type": "string",
            "title": "Access Token",
            "description": "访问令牌"
          },
          "refresh_token": {
            "type": "string",
            "title": "Refresh Token",
            "description": "刷新令牌"
          },
          "token_type": {
            "type": "string",
            "title": "Token Type",
            "description": "令牌类型",
            "default": "bearer"
          },
          "expires_in": {
            "type": "integer",
            "title": "Expires In",
            "description": "过期时间(秒)"
          }
        },
        "type": "object",
        "required": [
          "access_token",
          "refresh_token",
          "expires_in"
        ],
        "title": "Token",
        "description": "JWT令牌schema"
      },
      "UserCreate": {
        "properties": {
          "username": {
            "type": "string",
            "maxLength": 50,
            "minLength": 3,
            "title": "Username",
            "description": "用户名"
          },
          "email": {
            "type": "string",
            "format": "email",
            "title": "Email",
            "description": "邮箱地址"
          },
          "password": {
            "type": "string",
            "maxLength": 128,
            "minLength": 8,
            "title": "Password",
            "description": "密码"
          },
          "confirm_password": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Confirm Password",
            "description": "确认密码"
          }
        },
        "type": "object",
        "required": [
          "username",
          "email",
          "password"
        ],
        "title": "UserCreate",
        "description": "创建用户schema"
      },
      "UserLogin": {
        "properties": {
          "username": {
            "type": "string",
            "title": "Username",
            "description": "用户名或邮箱"
          },
          "password": {
            "type": "string",
            "title": "Password",
            "description": "密码"
          }
        },
        "type": "object",
        "required": [
          "username",
          "password"
        ],
        "title": "UserLogin",
        "description": "用户登录schema"
      },
      "UserRegisterResponse": {
        "properties": {
          "user": {
            "$ref": "#/components/schemas/UserResponse"
          },
          "token": {
            "$ref": "#/components/schemas/Token"
          }
        },
        "type": "object",
        "required": [
          "user",
          "token"
        ],
        "title": "UserRegisterResponse",
        "description": "用户注册响应schema"
      },
      "UserResponse": {
        "properties": {
          "username": {
            "type": "string",
            "maxLength": 50,
            "minLength": 3,
            "title": "Username",
            "description": "用户名"
          },
          "email": {
            "type": "string",
            "format": "email",
            "title": "Email",
            "description": "邮箱地址"
          },
          "id": {
            "type": "integer",
            "title": "Id",
            "description": "用户ID"
          },
          "is_active": {
            "type": "boolean",
            "title": "Is Active",
            "description": "是否激活"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "title": "Created At",
            "description": "创建时间"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Updated At",
            "description": "更新时间"
          },
          "deleted_at": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Deleted At",
            "description": "删除时间"
          },
          "extra_data": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Extra Data",
            "description": "扩展数据"
          }
        },
        "type": "object",
        "required": [
          "username",
          "email",
          "id",
          "is_active",
          "created_at",
          "updated_at"
        ],
        "title": "UserResponse",
        "description": "用户响应schema"
      },
      "UserUpdate": {
        "properties": {
          "username": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 50,
                "minLength": 3
              },
              {
                "type": "null"
              }
            ],
            "title": "Username",
            "description": "用户名"
          },
          "email": {
            "anyOf": [
              {
                "type": "string",
                "format": "email"
              },
              {
                "type": "null"
              }
            ],
            "title": "Email",
            "description": "邮箱地址"
          },
          "password": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 128,
                "minLength": 8
              },
              {
                "type": "null"
              }
            ],
            "title": "Password",
            "description": "新密码"
          },
          "current_password": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Current Password",
            "description": "当前密码（修改密码时必填）"
          }
        },
        "type": "object",
        "title": "UserUpdate",
        "description": "更新用户schema"
      },
      "ValidationError": {
        "properties": {
          "loc": {
            "items": {
              "anyOf": [
                {
                  "type": "string"
                },
                {
                  "type": "integer"
                }
              ]
            },
            "type": "array",
            "title": "Location"
          },
          "msg": {
            "type": "string",
            "title": "Message"
          },
          "type": {
            "type": "string",
            "title": "Error Type"
          }
        },
        "type": "object",
        "required": [
          "loc",
          "msg",
          "type"
        ],
        "title": "ValidationError"
      },
      "VulnerabilityCreate": {
        "properties": {
          "file_path": {
            "type": "string",
            "maxLength": 512,
            "title": "File Path",
            "description": "文件路径"
          },
          "line_number": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Line Number",
            "description": "行号"
          },
          "type": {
            "allOf": [
              {
                "$ref": "#/components/schemas/VulnerabilityType"
              }
            ],
            "description": "漏洞类型"
          },
          "description": {
            "type": "string",
            "title": "Description",
            "description": "漏洞描述"
          },
          "severity": {
            "allOf": [
              {
                "$ref": "#/components/schemas/VulnerabilitySeverity"
              }
            ],
            "description": "严重程度",
            "default": "medium"
          },
          "audit_id": {
            "type": "integer",
            "title": "Audit Id",
            "description": "审计ID"
          },
          "commit_id": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 64
              },
              {
                "type": "null"
              }
            ],
            "title": "Commit Id",
            "description": "提交ID"
          }
        },
        "type": "object",
        "required": [
          "file_path",
          "type",
          "description",
          "audit_id"
        ],
        "title": "VulnerabilityCreate",
        "description": "创建漏洞schema"
      },
      "VulnerabilityResponse": {
        "properties": {
          "file_path": {
            "type": "string",
            "maxLength": 512,
            "title": "File Path",
            "description": "文件路径"
          },
          "line_number": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Line Number",
            "description": "行号"
          },
          "type": {
            "allOf": [
              {
                "$ref": "#/components/schemas/VulnerabilityType"
              }
            ],
            "description": "漏洞类型"
          },
          "description": {
            "type": "string",
            "title": "Description",
            "description": "漏洞描述"
          },
          "severity": {
            "allOf": [
              {
                "$ref": "#/components/schemas/VulnerabilitySeverity"
              }
            ],
            "description": "严重程度",
            "default": "medium"
          },
          "id": {
            "type": "integer",
            "title": "Id",
            "description": "漏洞ID"
          },
          "audit_id": {
            "type": "integer",
            "title": "Audit Id",
            "description": "审计ID"
          },
          "status": {
            "allOf": [
              {
                "$ref": "#/components/schemas/VulnerabilityStatus"
              }
            ],
            "description": "漏洞状态"
          },
          "reason": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Reason",
            "description": "状态原因"
          },
          "commit_id": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Commit Id",
            "description": "提交ID"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "title": "Created At",
            "description": "创建时间"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "title": "Updated At",
            "description": "更新时间"
          },
          "deleted_at": {
            "anyOf": [
              {
                "type": "string",
                "format": "date-time"
              },
              {
                "type": "null"
              }
            ],
            "title": "Deleted At",
            "description": "删除时间"
          },
          "extra_data": {
            "anyOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Extra Data",
            "description": "扩展数据"
          }
        },
        "type": "object",
        "required": [
          "file_path",
          "type",
          "description",
          "id",
          "audit_id",
          "status",
          "created_at",
          "updated_at"
        ],
        "title": "VulnerabilityResponse",
        "description": "漏洞响应schema"
      },
      "VulnerabilitySeverity": {
        "type": "string",
        "enum": [
          "low",
          "medium",
          "high",
          "critical"
        ],
        "title": "VulnerabilitySeverity",
        "description": "漏洞严重程度枚举"
      },
      "VulnerabilityStatsResponse": {
        "properties": {
          "total": {
            "type": "integer",
            "title": "Total"
          },
          "by_severity": {
            "additionalProperties": {
              "type": "integer"
            },
            "type": "object",
            "title": "By Severity"
          },
          "by_status": {
            "additionalProperties": {
              "type": "integer"
            },
            "type": "object",
            "title": "By Status"
          },
          "by_type": {
            "additionalProperties": {
              "type": "integer"
            },
            "type": "object",
            "title": "By Type"
          },
          "recent_changes": {
            "type": "integer",
            "title": "Recent Changes"
          }
        },
        "type": "object",
        "required": [
          "total",
          "by_severity",
          "by_status",
          "by_type",
          "recent_changes"
        ],
        "title": "VulnerabilityStatsResponse"
      },
      "VulnerabilityStatus": {
        "type": "string",
        "enum": [
          "confirmed",
          "unexploitable",
          "manual_review",
          "unknown"
        ],
        "title": "VulnerabilityStatus",
        "description": "漏洞状态枚举"
      },
      "VulnerabilityType": {
        "type": "string",
        "enum": [
          "sensitive_info",
          "file_audit",
          "entry_point",
          "sensitive_func",
          "new",
          "fixed",
          "partial_fixed"
        ],
        "title": "VulnerabilityType",
        "description": "漏洞类型枚举"
      },
      "VulnerabilityUpdate": {
        "properties": {
          "status": {
            "anyOf": [
              {
                "$ref": "#/components/schemas/VulnerabilityStatus"
              },
              {
                "type": "null"
              }
            ],
            "description": "漏洞状态"
          },
          "reason": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Reason",
            "description": "状态原因"
          },
          "severity": {
            "anyOf": [
              {
                "$ref": "#/components/schemas/VulnerabilitySeverity"
              },
              {
                "type": "null"
              }
            ],
            "description": "严重程度"
          }
        },
        "type": "object",
        "title": "VulnerabilityUpdate",
        "description": "更新漏洞schema"
      },
      "VulnerabilityUpdateRequest": {
        "properties": {
          "status": {
            "type": "string",
            "title": "Status",
            "description": "新状态"
          },
          "notes": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Notes",
            "description": "更新备注"
          }
        },
        "type": "object",
        "required": [
          "status"
        ],
        "title": "VulnerabilityUpdateRequest"
      },
      "WebhookCreateRequest": {
        "properties": {
          "name": {
            "type": "string",
            "maxLength": 100,
            "minLength": 1,
            "title": "Name",
            "description": "Webhook名称"
          },
          "description": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 500
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "描述"
          },
          "url": {
            "type": "string",
            "maxLength": 2083,
            "minLength": 1,
            "format": "uri",
            "title": "Url",
            "description": "Webhook URL"
          },
          "events": {
            "items": {
              "type": "string"
            },
            "type": "array",
            "minItems": 1,
            "title": "Events",
            "description": "监听的事件类型"
          },
          "headers": {
            "anyOf": [
              {
                "additionalProperties": {
                  "type": "string"
                },
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Headers",
            "description": "自定义HTTP头"
          },
          "secret": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 255
              },
              {
                "type": "null"
              }
            ],
            "title": "Secret",
            "description": "签名密钥"
          },
          "timeout_seconds": {
            "type": "integer",
            "maximum": 300.0,
            "minimum": 5.0,
            "title": "Timeout Seconds",
            "description": "超时时间(秒)",
            "default": 30
          },
          "retry_count": {
            "type": "integer",
            "maximum": 10.0,
            "minimum": 0.0,
            "title": "Retry Count",
            "description": "重试次数",
            "default": 3
          }
        },
        "type": "object",
        "required": [
          "name",
          "url",
          "events"
        ],
        "title": "WebhookCreateRequest"
      },
      "WebhookEventResponse": {
        "properties": {
          "id": {
            "type": "integer",
            "title": "Id"
          },
          "event_type": {
            "type": "string",
            "title": "Event Type"
          },
          "status": {
            "type": "string",
            "title": "Status"
          },
          "status_code": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Status Code"
          },
          "error_message": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Error Message"
          },
          "sent_at": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Sent At"
          },
          "response_time_ms": {
            "anyOf": [
              {
                "type": "integer"
              },
              {
                "type": "null"
              }
            ],
            "title": "Response Time Ms"
          },
          "retry_count": {
            "type": "integer",
            "title": "Retry Count"
          },
          "created_at": {
            "type": "string",
            "title": "Created At"
          }
        },
        "type": "object",
        "required": [
          "id",
          "event_type",
          "status",
          "status_code",
          "error_message",
          "sent_at",
          "response_time_ms",
          "retry_count",
          "created_at"
        ],
        "title": "WebhookEventResponse"
      },
      "WebhookEventsListResponse": {
        "properties": {
          "events": {
            "items": {
              "additionalProperties": {
                "type": "string"
              },
              "type": "object"
            },
            "type": "array",
            "title": "Events"
          }
        },
        "type": "object",
        "required": [
          "events"
        ],
        "title": "WebhookEventsListResponse",
        "description": "可用事件类型列表"
      },
      "WebhookResponse": {
        "properties": {
          "id": {
            "type": "integer",
            "title": "Id"
          },
          "name": {
            "type": "string",
            "title": "Name"
          },
          "description": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Description"
          },
          "url": {
            "type": "string",
            "title": "Url"
          },
          "events": {
            "items": {
              "type": "string"
            },
            "type": "array",
            "title": "Events"
          },
          "is_active": {
            "type": "boolean",
            "title": "Is Active"
          },
          "last_status": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Last Status"
          },
          "last_sent_at": {
            "anyOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ],
            "title": "Last Sent At"
          },
          "success_count": {
            "type": "integer",
            "title": "Success Count"
          },
          "failure_count": {
            "type": "integer",
            "title": "Failure Count"
          },
          "timeout_seconds": {
            "type": "integer",
            "title": "Timeout Seconds"
          },
          "retry_count": {
            "type": "integer",
            "title": "Retry Count"
          },
          "created_at": {
            "type": "string",
            "title": "Created At"
          },
          "updated_at": {
            "type": "string",
            "title": "Updated At"
          }
        },
        "type": "object",
        "required": [
          "id",
          "name",
          "description",
          "url",
          "events",
          "is_active",
          "last_status",
          "last_sent_at",
          "success_count",
          "failure_count",
          "timeout_seconds",
          "retry_count",
          "created_at",
          "updated_at"
        ],
        "title": "WebhookResponse"
      },
      "WebhookUpdateRequest": {
        "properties": {
          "name": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 100,
                "minLength": 1
              },
              {
                "type": "null"
              }
            ],
            "title": "Name",
            "description": "Webhook名称"
          },
          "description": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 500
              },
              {
                "type": "null"
              }
            ],
            "title": "Description",
            "description": "描述"
          },
          "url": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 2083,
                "minLength": 1,
                "format": "uri"
              },
              {
                "type": "null"
              }
            ],
            "title": "Url",
            "description": "Webhook URL"
          },
          "events": {
            "anyOf": [
              {
                "items": {
                  "type": "string"
                },
                "type": "array",
                "minItems": 1
              },
              {
                "type": "null"
              }
            ],
            "title": "Events",
            "description": "监听的事件类型"
          },
          "headers": {
            "anyOf": [
              {
                "additionalProperties": {
                  "type": "string"
                },
                "type": "object"
              },
              {
                "type": "null"
              }
            ],
            "title": "Headers",
            "description": "自定义HTTP头"
          },
          "secret": {
            "anyOf": [
              {
                "type": "string",
                "maxLength": 255
              },
              {
                "type": "null"
              }
            ],
            "title": "Secret",
            "description": "签名密钥"
          },
          "timeout_seconds": {
            "anyOf": [
              {
                "type": "integer",
                "maximum": 300.0,
                "minimum": 5.0
              },
              {
                "type": "null"
              }
            ],
            "title": "Timeout Seconds",
            "description": "超时时间(秒)"
          },
          "retry_count": {
            "anyOf": [
              {
                "type": "integer",
                "maximum": 10.0,
                "minimum": 0.0
              },
              {
                "type": "null"
              }
            ],
            "title": "Retry Count",
            "description": "重试次数"
          },
          "is_active": {
            "anyOf": [
              {
                "type": "boolean"
              },
              {
                "type": "null"
              }
            ],
            "title": "Is Active",
            "description": "是否启用"
          }
        },
        "type": "object",
        "title": "WebhookUpdateRequest"
      }
    },
    "securitySchemes": {
      "BearerAuth": {
        "type": "http",
        "scheme": "bearer",
        "bearerFormat": "JWT",
        "description": "JWT认证，格式：Bearer <token>"
      }
    }
  },
  "x-logo": {
    "url": "/static/logo.png",
    "altText": "VulnAuditBox Logo"
  },
  "servers": [
    {
      "url": "http://localhost:8000",
      "description": "开发环境"
    },
    {
      "url": "https://api.vulnauditbox.com",
      "description": "生产环境"
    }
  ]
}