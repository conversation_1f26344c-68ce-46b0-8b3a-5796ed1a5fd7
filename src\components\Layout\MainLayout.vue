<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :width="240"
      :collapsed-width="80"
      theme="light"
      class="layout-sider"
    >
      <!-- Logo区域 -->
      <div class="logo">
        <img
          v-if="!collapsed"
          src="/logo.svg"
          alt="VulnAuditBox"
          class="logo-img"
        />
        <img
          v-else
          src="/logo.svg"
          alt="VAB"
          class="logo-mini"
        />
      </div>
      
      <!-- 导航菜单 -->
      <NavBar :collapsed="collapsed" />
    </a-layout-sider>
    
    <!-- 主内容区域 -->
    <a-layout :class="['layout-content', { collapsed }]">
      <!-- 顶部导航栏 -->
      <a-layout-header :class="['layout-header', { collapsed }]">
        <div class="header-left">
          <a-button
            type="text"
            @click="toggleCollapsed"
            class="trigger"
          >
            <template #icon>
              <MenuUnfoldOutlined v-if="collapsed" />
              <MenuFoldOutlined v-else />
            </template>
          </a-button>
          
          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              <router-link v-if="item.path && item.path !== $route.path" :to="item.path">
                {{ item.title }}
              </router-link>
              <span v-else>{{ item.title }}</span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        
        <div class="header-center">
          <!-- 全局搜索 -->
          <GlobalSearch />
        </div>

        <div class="header-right">
          <!-- 主题切换 -->
          <ThemeToggle mode="dropdown" />

          <!-- 通知中心 -->
          <Notification />

          <!-- 用户菜单 -->
          <a-dropdown placement="bottomRight">
            <a-space class="user-info">
              <a-avatar :src="userStore.user?.avatar" :size="32">
                {{ userStore.user?.username?.charAt(0).toUpperCase() }}
              </a-avatar>
              <span v-if="!collapsed" class="username">
                {{ userStore.user?.username }}
              </span>
              <DownOutlined />
            </a-space>

            <template #overlay>
              <a-menu>
                <a-menu-item key="profile" @click="$router.push('/profile')">
                  <template #icon>
                    <UserOutlined />
                  </template>
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings" @click="$router.push('/system/settings')">
                  <template #icon>
                    <SettingOutlined />
                  </template>
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <template #icon>
                    <LogoutOutlined />
                  </template>
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 主内容 -->
      <a-layout-content class="main-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component, route }">
            <transition name="fade" mode="out-in">
              <keep-alive :include="keepAliveComponents">
                <component :is="Component" :key="route.path" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </a-layout-content>
      
      <!-- 底部 -->
      <a-layout-footer :class="['layout-footer', { collapsed }]">
        <div class="footer-content">
          <span>© 2024 VulnAuditBox. All rights reserved.</span>
          <a-space>
            <a href="/docs" target="_blank">文档</a>
            <a href="/api" target="_blank">API</a>
            <a href="/support" target="_blank">支持</a>
          </a-space>
        </div>
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useWebSocketStore } from '@/stores/websocket'
import NavBar from '@/components/NavBar.vue'
import Notification from '@/components/Notification.vue'
import GlobalSearch from '@/components/GlobalSearch.vue'
import ThemeToggle from '@/components/ThemeToggle.vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DownOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const webSocketStore = useWebSocketStore()

const collapsed = ref(false)

// 需要缓存的组件
const keepAliveComponents = ref(['Dashboard', 'ProjectList', 'AuditList'])

// 面包屑导航
const breadcrumbItems = computed(() => {
  const pathSegments = route.path.split('/').filter(Boolean)
  const items = [{ title: '首页', path: '/dashboard' }]
  
  let currentPath = ''
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`
    
    // 根据路径生成面包屑标题
    let title = segment
    switch (segment) {
      case 'dashboard':
        title = '仪表板'
        break
      case 'project':
        title = '项目管理'
        break
      case 'audit':
        title = '审计管理'
        break
      case 'vulnerability':
        title = '漏洞管理'
        break
      case 'report':
        title = '报告管理'
        break
      case 'webhook':
        title = 'Webhook管理'
        break
      case 'list':
        title = '列表'
        break
      case 'create':
        title = '创建'
        break
      case 'detail':
        title = '详情'
        break
      case 'generate':
        title = '生成'
        break
      default:
        // 如果是数字ID，显示为ID
        if (/^\d+$/.test(segment)) {
          title = `#${segment}`
        }
    }
    
    items.push({
      title,
      path: index === pathSegments.length - 1 ? '' : currentPath
    })
  })
  
  return items
})

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
  localStorage.setItem('sidebarCollapsed', collapsed.value.toString())
}

// 退出登录
const handleLogout = async () => {
  try {
    await userStore.logout()
    webSocketStore.disconnect()
    router.push('/login')
  } catch (error) {
    console.error('Logout error:', error)
  }
}

// 监听路由变化，更新页面标题
watch(
  () => route.meta.title,
  (title) => {
    if (title) {
      document.title = `${title} - VulnAuditBox`
    }
  },
  { immediate: true }
)

// 响应式处理
const handleResize = () => {
  const width = window.innerWidth
  if (width < 768) {
    collapsed.value = true
  }
}

onMounted(() => {
  // 恢复侧边栏状态
  const savedCollapsed = localStorage.getItem('sidebarCollapsed')
  if (savedCollapsed !== null) {
    collapsed.value = savedCollapsed === 'true'
  }
  
  // 初始化WebSocket连接
  if (userStore.isLoggedIn) {
    webSocketStore.connect()
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  handleResize()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  webSocketStore.disconnect()
})
</script>

<style scoped>
.main-layout {
  height: 100vh;
  overflow: hidden;
}

.layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100vh;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

[data-theme='dark'] .logo {
  border-bottom-color: #303030;
}

.logo-img {
  height: 32px;
  width: auto;
}

.logo-mini {
  height: 24px;
  width: 24px;
}

.layout-content {
  margin-left: 240px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-content.collapsed {
  margin-left: 80px;
}

.layout-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 99;
  position: fixed;
  top: 0;
  right: 0;
  left: 240px;
  height: 64px;
}

.layout-header.collapsed {
  left: 80px;
}

[data-theme='dark'] .layout-header {
  background: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 0 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

.trigger {
  font-size: 18px;
  margin-right: 16px;
}

.breadcrumb {
  margin: 0;
}

.user-info {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

[data-theme='dark'] .user-info:hover {
  background-color: #262626;
}

.username {
  font-weight: 500;
}

.main-content {
  flex: 1;
  margin-top: 64px;
  margin-bottom: 70px;
  padding: 0;
  background: #f0f2f5;
  overflow-y: auto;
  overflow-x: hidden;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

[data-theme='dark'] .main-content {
  background: #141414;
}

.content-wrapper {
  padding: 24px;
  min-height: 100%;
}

.layout-footer {
  background: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px;
  position: fixed;
  bottom: 0;
  right: 0;
  left: 240px;
  height: 70px;
  z-index: 98;
}

.layout-footer.collapsed {
  left: 80px;
}

[data-theme='dark'] .layout-footer {
  background: #1f1f1f;
  border-top-color: #303030;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #666;
  font-size: 14px;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    position: fixed;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .layout-sider:not(.ant-layout-sider-collapsed) {
    transform: translateX(0);
  }

  .layout-content {
    margin-left: 0 !important;
  }

  .layout-header {
    left: 0 !important;
    padding: 0 16px;
  }

  .layout-footer {
    left: 0 !important;
  }

  .header-left .breadcrumb {
    display: none;
  }

  .header-center {
    padding: 0 16px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .footer-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .header-center {
    display: none;
  }

  .username {
    display: none;
  }

  .content-wrapper {
    padding: 12px;
  }
}
</style>
