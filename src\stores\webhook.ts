import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { 
  Webhook,
  WebhookCreateRequest,
  WebhookUpdateRequest,
  WebhookEventResponse,
  WebhookQueryParams,
  WebhookTestRequest,
  WebhookEventType
} from '@/types/webhook'
import { webhookApi } from '@/api/webhook'
import { message } from 'ant-design-vue'

export const useWebhookStore = defineStore('webhook', () => {
  const webhooks = ref<Webhook[]>([])
  const currentWebhook = ref<Webhook | null>(null)
  const webhookEvents = ref<WebhookEventResponse[]>([])
  const availableEvents = ref<WebhookEventType[]>([])
  const loading = ref(false)
  const total = ref(0)

  // 获取Webhook列表
  const fetchWebhooks = async (params?: WebhookQueryParams) => {
    loading.value = true
    try {
      const response = await webhookApi.getWebhooks(params)
      webhooks.value = response.data
      total.value = response.data.length
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取Webhook列表失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取Webhook详情
  const fetchWebhook = async (id: number) => {
    loading.value = true
    try {
      const response = await webhookApi.getWebhook(id)
      currentWebhook.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取Webhook详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 创建Webhook
  const createWebhook = async (webhookForm: WebhookCreateRequest) => {
    loading.value = true
    try {
      const response = await webhookApi.createWebhook(webhookForm)
      webhooks.value.unshift(response.data)
      message.success('Webhook创建成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '创建Webhook失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新Webhook
  const updateWebhook = async (id: number, webhookData: WebhookUpdateRequest) => {
    loading.value = true
    try {
      const response = await webhookApi.updateWebhook(id, webhookData)
      
      // 更新本地状态
      const index = webhooks.value.findIndex(w => w.id === id)
      if (index !== -1) {
        webhooks.value[index] = response.data
      }
      
      if (currentWebhook.value?.id === id) {
        currentWebhook.value = response.data
      }
      
      message.success('Webhook更新成功')
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '更新Webhook失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除Webhook
  const deleteWebhook = async (id: number) => {
    loading.value = true
    try {
      await webhookApi.deleteWebhook(id)
      webhooks.value = webhooks.value.filter(w => w.id !== id)
      
      if (currentWebhook.value?.id === id) {
        currentWebhook.value = null
      }
      
      message.success('Webhook删除成功')
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '删除Webhook失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 测试Webhook
  const testWebhook = async (id: number, testData?: WebhookTestRequest) => {
    loading.value = true
    try {
      const response = await webhookApi.testWebhook(id, testData)
      
      if (response.data.success) {
        message.success(`Webhook测试成功 (${response.data.response_time_ms}ms)`)
      } else {
        message.warning(`Webhook测试失败: ${response.data.error_message}`)
      }
      
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || 'Webhook测试失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取Webhook事件历史
  const fetchWebhookEvents = async (id: number, params?: { skip?: number; limit?: number }) => {
    loading.value = true
    try {
      const response = await webhookApi.getWebhookEvents(id, params)
      webhookEvents.value = response.data
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取事件历史失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取可用事件类型
  const fetchAvailableEvents = async () => {
    try {
      const response = await webhookApi.getAvailableEvents()
      // 转换API响应为事件类型数组
      availableEvents.value = response.data.events.map(event => ({
        key: Object.keys(event)[0],
        name: Object.values(event)[0] as string,
        description: Object.values(event)[0] as string
      }))
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '获取事件类型失败')
      throw error
    }
  }

  // 切换Webhook状态
  const toggleWebhook = async (id: number, is_active: boolean) => {
    try {
      const response = await webhookApi.toggleWebhook(id, is_active)
      
      // 更新本地状态
      const index = webhooks.value.findIndex(w => w.id === id)
      if (index !== -1) {
        webhooks.value[index] = response.data
      }
      
      if (currentWebhook.value?.id === id) {
        currentWebhook.value = response.data
      }
      
      message.success(`Webhook已${is_active ? '启用' : '禁用'}`)
      return response
    } catch (error: any) {
      message.error(error.response?.data?.detail || error.message || '切换状态失败')
      throw error
    }
  }

  return {
    webhooks,
    currentWebhook,
    webhookEvents,
    availableEvents,
    loading,
    total,
    fetchWebhooks,
    fetchWebhook,
    createWebhook,
    updateWebhook,
    deleteWebhook,
    testWebhook,
    fetchWebhookEvents,
    fetchAvailableEvents,
    toggleWebhook
  }
})
