import type { Directive } from 'vue'

interface ClickOutsideElement extends HTMLElement {
  clickOutsideHandler?: (event: Event) => void
}

const clickOutside: Directive = {
  mounted(el: ClickOutsideElement, binding) {
    el.clickOutsideHandler = (event: Event) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value(event)
      }
    }
    document.addEventListener('click', el.clickOutsideHandler)
  },
  unmounted(el: ClickOutsideElement) {
    if (el.clickOutsideHandler) {
      document.removeEventListener('click', el.clickOutsideHandler)
      delete el.clickOutsideHandler
    }
  }
}

export default clickOutside
