<template>
  <div class="app-header">
    <div class="header-left">
      <a-button
        type="text"
        @click="appStore.toggleSidebar"
        class="trigger"
      >
        <MenuUnfoldOutlined v-if="appStore.sidebarCollapsed" />
        <MenuFoldOutlined v-else />
      </a-button>
      
      <a-breadcrumb class="breadcrumb">
        <a-breadcrumb-item v-for="item in breadcrumbs" :key="item.path">
          <router-link v-if="item.path" :to="item.path">
            {{ item.title }}
          </router-link>
          <span v-else>{{ item.title }}</span>
        </a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    
    <div class="header-right">
      <a-space>
        <a-button
          type="text"
          @click="appStore.toggleDarkMode"
          class="action-btn"
        >
          <BulbOutlined v-if="appStore.isDarkMode" />
          <BulbFilled v-else />
        </a-button>
        
        <a-badge :count="notifications.length" :offset="[-5, 5]">
          <a-button type="text" class="action-btn">
            <BellOutlined />
          </a-button>
        </a-badge>
        
        <a-dropdown>
          <a-button type="text" class="user-info">
            <a-avatar :src="userStore.user?.avatar" :size="32">
              {{ userStore.user?.username?.charAt(0).toUpperCase() }}
            </a-avatar>
            <span class="username">{{ userStore.user?.username }}</span>
            <DownOutlined />
          </a-button>
          
          <template #overlay>
            <a-menu>
              <a-menu-item key="profile" @click="$router.push('/user/profile')">
                <UserOutlined />
                个人资料
              </a-menu-item>
              <a-menu-item key="settings" @click="$router.push('/settings')">
                <UserOutlined />
                系统设置
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout" @click="handleLogout">
                <LogoutOutlined />
                退出登录
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BulbOutlined,
  BulbFilled,
  BellOutlined,
  UserOutlined,
  LogoutOutlined,
  DownOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

const notifications = ref([])

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    title: item.meta?.title,
    path: item.path === route.path ? '' : item.path
  }))
})

const handleLogout = () => {
  userStore.logout()
  router.push('/user/login')
}
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  margin-right: 24px;
}

.breadcrumb {
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.action-btn {
  font-size: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
}

.username {
  margin-left: 8px;
  font-weight: 500;
}
</style>
