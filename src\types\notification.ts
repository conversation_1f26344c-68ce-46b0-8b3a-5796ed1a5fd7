// 通知类型定义
export interface NotificationItem {
  id: string
  type: 'info' | 'success' | 'warning' | 'error' | 'audit' | 'project' | 'report' | 'webhook'
  message: string
  description?: string
  actionUrl?: string
  read: boolean
  createdAt: string
  updatedAt?: string
}

export interface NotificationSettings {
  enableDesktop: boolean
  enableSound: boolean
  enableEmail: boolean
  auditCompleted: boolean
  auditFailed: boolean
  vulnerabilityFound: boolean
  reportGenerated: boolean
  webhookFailed: boolean
}

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
}
