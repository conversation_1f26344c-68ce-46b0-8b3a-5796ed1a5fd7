# VulnAuditBox Web Frontend

VulnAuditBox是一个AI驱动的代码审计平台的前端应用，基于Vue 3 + TypeScript构建。

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - JavaScript的超集，提供静态类型检查
- **Vite** - 下一代前端构建工具
- **Vue Router** - Vue.js官方路由管理器
- **Pinia** - Vue的状态管理库
- **Ant Design Vue** - 企业级UI组件库
- **Axios** - HTTP客户端
- **ECharts** - 数据可视化图表库
- **Socket.io** - 实时通信
- **Marked** - Markdown解析器

## 项目结构

```
src/
├── api/                 # API接口定义
├── assets/             # 静态资源
├── components/         # 通用组件
├── layouts/           # 布局组件
├── router/            # 路由配置
├── stores/            # Pinia状态管理
├── types/             # TypeScript类型定义
├── utils/             # 工具函数
├── views/             # 页面组件
├── App.vue            # 根组件
└── main.ts            # 应用入口
```

## 开发环境设置

### 前置要求

- Node.js >= 18.0.0
- npm 或 yarn 或 pnpm

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
# 或
yarn build
# 或
pnpm build
```

### 预览生产构建

```bash
npm run preview
# 或
yarn preview
# 或
pnpm preview
```

## 功能特性

- ✅ 用户认证（JWT）
- ✅ 响应式设计
- ✅ 暗黑模式支持
- ✅ 国际化准备
- ✅ 路由守卫
- ✅ API代理配置
- ✅ 状态管理
- ✅ 组件化架构
- ✅ TypeScript支持
- ✅ 代码规范检查

## API代理配置

开发环境下，前端请求会自动代理到后端服务：

- 前端: http://localhost:3000
- 后端: http://localhost:8080
- API代理: `/api/*` → `http://localhost:8080/api/*`

## 环境变量

项目支持以下环境变量：

- `VITE_APP_TITLE` - 应用标题
- `VITE_API_BASE_URL` - API基础URL
- `VITE_API_TIMEOUT` - API请求超时时间
- `VITE_WS_URL` - WebSocket连接URL

## 开发规范

### 代码风格

- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 遵循Vue 3 Composition API最佳实践

### 组件开发

- 使用`<script setup>`语法
- 合理使用TypeScript类型定义
- 组件命名使用PascalCase
- 文件命名使用kebab-case

### 状态管理

- 使用Pinia进行状态管理
- 按功能模块划分store
- 使用Composition API风格

## 部署

### 构建

```bash
npm run build
```

构建产物将生成在`dist`目录中。

### 部署到静态服务器

将`dist`目录的内容部署到任何静态文件服务器即可。

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 贡献指南

1. Fork本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
