import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 格式化日期
export const formatDate = (date: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  return dayjs(date).format(format)
}

// 相对时间
export const fromNow = (date: string | Date): string => {
  return dayjs(date).fromNow()
}

// 获取今天开始时间
export const getToday = (): Date => {
  return dayjs().startOf('day').toDate()
}

// 获取本周开始时间
export const getThisWeek = (): Date => {
  return dayjs().startOf('week').toDate()
}

// 获取本月开始时间
export const getThisMonth = (): Date => {
  return dayjs().startOf('month').toDate()
}

// 获取本年开始时间
export const getThisYear = (): Date => {
  return dayjs().startOf('year').toDate()
}

// 计算两个日期之间的差异
export const diffDays = (date1: string | Date, date2: string | Date): number => {
  return dayjs(date1).diff(dayjs(date2), 'day')
}

// 检查日期是否为今天
export const isToday = (date: string | Date): boolean => {
  return dayjs(date).isSame(dayjs(), 'day')
}

// 检查日期是否为本周
export const isThisWeek = (date: string | Date): boolean => {
  return dayjs(date).isSame(dayjs(), 'week')
}

// 检查日期是否为本月
export const isThisMonth = (date: string | Date): boolean => {
  return dayjs(date).isSame(dayjs(), 'month')
}
