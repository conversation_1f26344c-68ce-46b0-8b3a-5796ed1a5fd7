<template>
  <div class="project-index">
    <a-card title="项目管理">
      <p>这是项目管理的入口页面，包含以下功能：</p>
      <a-space direction="vertical" size="middle">
        <router-link to="/project/list">
          <a-button type="primary" size="large">
            <template #icon>
              <UnorderedListOutlined />
            </template>
            项目列表
          </a-button>
        </router-link>
        <router-link to="/project/create">
          <a-button type="default" size="large">
            <template #icon>
              <PlusOutlined />
            </template>
            创建项目
          </a-button>
        </router-link>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { UnorderedListOutlined, PlusOutlined } from '@ant-design/icons-vue'
</script>

<style scoped>
.project-index {
  max-width: 600px;
  margin: 0 auto;
}
</style>
