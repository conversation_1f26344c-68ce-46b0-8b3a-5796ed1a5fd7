<template>
  <div class="audit-list">
    <div class="audit-header">
      <h2>审计任务</h2>
      <a-space>
        <a-button @click="handleRefresh" :loading="auditStore.loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="$router.push('/audit/create')">
          <template #icon>
            <PlusOutlined />
          </template>
          创建审计
        </a-button>
      </a-space>
    </div>
    
    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="statistics-row" v-if="statistics">
      <a-col :xs="24" :sm="12" :lg="4">
        <a-card size="small">
          <a-statistic
            title="总审计数"
            :value="statistics.total_audits"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="4">
        <a-card size="small">
          <a-statistic
            title="待处理"
            :value="statistics.pending_audits"
            :value-style="{ color: '#faad14' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="4">
        <a-card size="small">
          <a-statistic
            title="运行中"
            :value="statistics.running_audits"
            :value-style="{ color: '#52c41a' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="4">
        <a-card size="small">
          <a-statistic
            title="已完成"
            :value="statistics.completed_audits"
            :value-style="{ color: '#13c2c2' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="4">
        <a-card size="small">
          <a-statistic
            title="失败"
            :value="statistics.failed_audits"
            :value-style="{ color: '#f5222d' }"
          />
        </a-card>
      </a-col>
      <a-col :xs="24" :sm="12" :lg="4">
        <a-card size="small">
          <a-statistic
            title="总漏洞数"
            :value="statistics.total_vulnerabilities"
            :value-style="{ color: '#722ed1' }"
          />
        </a-card>
      </a-col>
    </a-row>
    
    <a-card>
      <div class="audit-toolbar">
        <a-space>
          <a-select
            v-model:value="projectFilter"
            placeholder="选择项目"
            style="width: 200px"
            @change="handleSearch"
            allow-clear
            show-search
            :filter-option="filterProjectOption"
          >
            <a-select-option 
              v-for="project in projects" 
              :key="project.id" 
              :value="project.id"
            >
              {{ project.name }}
            </a-select-option>
          </a-select>
          
          <a-select
            v-model:value="statusFilter"
            placeholder="审计状态"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部状态</a-select-option>
            <a-select-option value="pending">待处理</a-select-option>
            <a-select-option value="running">运行中</a-select-option>
            <a-select-option value="completed">已完成</a-select-option>
            <a-select-option value="failed">失败</a-select-option>
          </a-select>
          
          <a-select
            v-model:value="typeFilter"
            placeholder="审计类型"
            style="width: 120px"
            @change="handleSearch"
            allow-clear
          >
            <a-select-option value="">全部类型</a-select-option>
            <a-select-option value="initial">初始审计</a-select-option>
            <a-select-option value="update">更新审计</a-select-option>
          </a-select>
        </a-space>
      </div>
      
      <a-table
        :columns="columns"
        :data-source="auditStore.audits"
        :loading="auditStore.loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'id'">
            <a @click="$router.push(`/audit/detail/${record.id}`)">
              #{{ record.id }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'project_name'">
            <a @click="$router.push(`/project/detail/${record.project_id}`)">
              {{ record.project_name || `项目 #${record.project_id}` }}
            </a>
          </template>
          
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>
          
          <template v-else-if="column.key === 'status'">
            <a-space>
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
              <a-progress
                v-if="record.status === 'running' && auditStore.auditProgress[record.id]"
                :percent="auditStore.auditProgress[record.id].progress"
                size="small"
                :show-info="false"
                style="width: 60px"
              />
            </a-space>
          </template>
          
          <template v-else-if="column.key === 'duration'">
            <span v-if="record.duration">
              {{ formatDuration(record.duration) }}
            </span>
            <span v-else-if="record.status === 'running' && record.start_time">
              {{ formatDuration(getRunningDuration(record.start_time)) }}
            </span>
            <span v-else class="text-gray">-</span>
          </template>
          
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a @click="$router.push(`/audit/detail/${record.id}`)">查看</a>
              <a 
                v-if="record.status === 'running'" 
                @click="handleCancel(record.id)"
                style="color: #ff4d4f"
              >
                取消
              </a>
              <a 
                v-if="record.status === 'failed'" 
                @click="handleRerun(record.id)"
              >
                重新运行
              </a>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuditStore } from '@/stores/audit'
import { useProjectStore } from '@/stores/project'
import { formatDate } from '@/utils/date'
import { websocketManager, connectWebSocket } from '@/utils/websocket'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import type { AuditQueryParams, AuditStatus, AuditType } from '@/types/audit'
import type { TableColumnsType } from 'ant-design-vue'
import { Modal } from 'ant-design-vue'

const router = useRouter()
const auditStore = useAuditStore()
const projectStore = useProjectStore()

const projectFilter = ref<number>()
const statusFilter = ref<AuditStatus>()
const typeFilter = ref<AuditType>()

const statistics = computed(() => auditStore.statistics)
const projects = computed(() => projectStore.projects)

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

const columns: TableColumnsType = [
  {
    title: '审计ID',
    dataIndex: 'id',
    key: 'id',
    width: 100,
    sorter: (a: any, b: any) => a.id - b.id
  },
  {
    title: '项目',
    key: 'project_name',
    width: 200,
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    filters: [
      { text: '初始审计', value: 'initial' },
      { text: '更新审计', value: 'update' }
    ],
    onFilter: (value: string, record: any) => record.type === value
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 150,
    filters: [
      { text: '待处理', value: 'pending' },
      { text: '运行中', value: 'running' },
      { text: '已完成', value: 'completed' },
      { text: '失败', value: 'failed' }
    ],
    onFilter: (value: string, record: any) => record.status === value
  },
  {
    title: '耗时',
    key: 'duration',
    width: 100
  },
  {
    title: '漏洞数',
    dataIndex: 'vulnerability_count',
    key: 'vulnerability_count',
    width: 80,
    sorter: (a: any, b: any) => (a.vulnerability_count || 0) - (b.vulnerability_count || 0)
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at',
    width: 180,
    sorter: (a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

const getTypeColor = (type: AuditType) => {
  const colorMap: Record<AuditType, string> = {
    initial: 'blue',
    update: 'green'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: AuditType) => {
  const textMap: Record<AuditType, string> = {
    initial: '初始审计',
    update: '更新审计'
  }
  return textMap[type] || type
}

const getStatusColor = (status: AuditStatus) => {
  const colorMap: Record<AuditStatus, string> = {
    pending: 'orange',
    running: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: AuditStatus) => {
  const textMap: Record<AuditStatus, string> = {
    pending: '待处理',
    running: '运行中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || status
}

const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}时${minutes}分`
  }
}

const getRunningDuration = (startTime: string) => {
  const start = new Date(startTime).getTime()
  const now = Date.now()
  return Math.floor((now - start) / 1000)
}

const filterProjectOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const handleSearch = () => {
  fetchAudits()
}

const handleRefresh = () => {
  fetchAudits()
  auditStore.fetchStatistics()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchAudits()
}

const handleCancel = (id: number) => {
  Modal.confirm({
    title: '确认取消',
    content: '确定要取消这个审计任务吗？',
    onOk: async () => {
      try {
        await auditStore.cancelAudit(id)
        fetchAudits()
      } catch (error) {
        console.error('Cancel audit error:', error)
      }
    }
  })
}

const handleRerun = (id: number) => {
  Modal.confirm({
    title: '确认重新运行',
    content: '确定要重新运行这个审计任务吗？',
    onOk: async () => {
      try {
        await auditStore.rerunAudit(id)
        fetchAudits()
      } catch (error) {
        console.error('Rerun audit error:', error)
      }
    }
  })
}

const fetchAudits = async () => {
  const params: AuditQueryParams = {
    skip: (pagination.current - 1) * pagination.pageSize,
    limit: pagination.pageSize
  }
  
  if (projectFilter.value) {
    params.project_id = projectFilter.value
  }
  
  if (statusFilter.value) {
    params.status = statusFilter.value
  }
  
  if (typeFilter.value) {
    params.type = typeFilter.value
  }
  
  try {
    await auditStore.fetchAudits(params)
    pagination.total = auditStore.total
  } catch (error) {
    console.error('Fetch audits error:', error)
  }
}

onMounted(async () => {
  // 获取项目列表用于过滤
  await projectStore.fetchProjects()
  
  // 获取审计列表和统计
  await fetchAudits()
  await auditStore.fetchStatistics()
  
  // 连接WebSocket并初始化监听器
  try {
    await connectWebSocket()
    auditStore.initWebSocketListeners()
  } catch (error) {
    console.error('WebSocket connection failed:', error)
  }
})

onUnmounted(() => {
  // 断开WebSocket连接
  websocketManager.disconnect()
})
</script>

<style scoped>
.audit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.audit-header h2 {
  margin: 0;
}

.statistics-row {
  margin-bottom: 16px;
}

.audit-toolbar {
  margin-bottom: 16px;
}

.text-gray {
  color: #999;
}

:deep(.ant-table-cell) {
  padding: 12px 8px;
}

:deep(.ant-statistic-content) {
  font-size: 20px;
}
</style>
