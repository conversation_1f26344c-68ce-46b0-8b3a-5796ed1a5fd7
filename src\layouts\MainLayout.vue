<template>
  <a-layout class="main-layout">
    <a-layout-sider
      v-model:collapsed="appStore.sidebarCollapsed"
      :trigger="null"
      collapsible
      class="sidebar"
    >
      <div class="logo">
        <img src="/logo.svg" alt="VulnAuditBox" />
        <span v-if="!appStore.sidebarCollapsed">VulnAuditBox</span>
      </div>
      <AppSidebar />
    </a-layout-sider>
    
    <a-layout>
      <a-layout-header class="header">
        <AppHeader />
      </a-layout-header>
      
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import AppHeader from '@/components/AppHeader.vue'
import AppSidebar from '@/components/AppSidebar.vue'

const appStore = useAppStore()

onMounted(() => {
  appStore.initializeApp()
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
}

.sidebar {
  background: #001529;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #1f1f1f;
}

.logo img {
  height: 32px;
  margin-right: 8px;
}

.header {
  background: #fff;
  padding: 0;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  min-height: 280px;
  border-radius: 6px;
}
</style>
