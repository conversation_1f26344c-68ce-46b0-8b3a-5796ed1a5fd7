// 基于API文档的项目类型定义
export type ProjectType = 'git' | 'docker'

export interface Project {
  id: number
  name: string
  type: ProjectType
  user_id: number
  git_url?: string
  git_branch?: string
  docker_image?: string
  source_path?: string
  current_hash?: string
  check_interval: number
  description?: string
  extra_data?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface CreateProjectForm {
  name: string
  type: ProjectType
  git_url?: string
  git_branch?: string
  docker_image?: string
  source_path?: string
  check_interval?: number
  description?: string
  extra_data?: Record<string, any>
}

export interface UpdateProjectForm {
  name?: string
  git_url?: string
  git_branch?: string
  docker_image?: string
  source_path?: string
  check_interval?: number
  description?: string
}

// Git仓库信息
export interface GitRepositoryInfo {
  url: string
  branch: string
  current_hash?: string
  last_commit_message?: string
  last_commit_author?: string
  last_commit_date?: string
  is_accessible: boolean
}

// 项目查询参数
export interface ProjectQueryParams {
  skip?: number
  limit?: number
  project_type?: ProjectType
  search?: string
}

// 项目列表响应
export interface ProjectListResponse {
  items: Project[]
  total: number
  page: number
  pageSize: number
}
